import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { FaStar, FaStarHalfAlt, FaRegStar } from 'react-icons/fa';
import { getProductDetails } from '../features/products/productSlice';
import { addToCart } from '../features/cart/cartSlice';
import Spinner from '../components/Spinner';

const ProductDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [qty, setQty] = useState(1);

  const { product, isLoading, isError, message } = useSelector(
    (state) => state.products
  );

  useEffect(() => {
    if (isError) {
      toast.error(message);
    }
    console.log('Product:', product);

    dispatch(getProductDetails(id));
  }, [dispatch, id, isError, message]);

  const addToCartHandler = () => {
    // Make sure we have a valid product ID
    if (!product || !product._id) {
      toast.error('Invalid product data');
      return;
    }

    // Create cart item with proper structure
    const cartItem = {
      product: product._id,
      name: product.name,
      image: product.images && product.images.length > 0
        ? product.images[0].url
        : 'https://via.placeholder.com/300',
      price: product.price,
      pointValue: product.pointValue || 0,
      stock: product.stock || 0,
      qty: qty,
      variant: null // Add variant if needed
    };

    console.log('Adding to cart:', cartItem);
    dispatch(addToCart(cartItem));
    navigate('/cart');
  };

  // Rating stars component
  const renderRatingStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 1; i <= 5; i++) {
      if (i <= fullStars) {
        stars.push(<FaStar key={i} className="text-yellow-400" />);
      } else if (i === fullStars + 1 && hasHalfStar) {
        stars.push(<FaStarHalfAlt key={i} className="text-yellow-400" />);
      } else {
        stars.push(<FaRegStar key={i} className="text-yellow-400" />);
      }
    }

    return stars;
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-96">
        <Spinner />
      </div>
    );
  }

  if (!product) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          Product not found
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <button
        onClick={() => navigate(-1)}
        className="mb-6 inline-flex items-center text-blue-600 hover:underline"
      >
        &larr; Back
      </button>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="bg-white rounded-lg overflow-hidden shadow-md">
          <img
            src={product.images && product.images.length > 0
              ? product.images[0].url
              : 'https://via.placeholder.com/300'}
            alt={product.name}
            className="w-full h-auto object-cover"
          />
        </div>

        <div>
          <h1 className="text-3xl font-bold mb-4">{product.name}</h1>

          <div className="flex items-center mb-4">
            <div className="flex mr-2">
              {renderRatingStars(product.rating)}
            </div>
            <span className="text-gray-600">
              {product.numReviews} {product.numReviews === 1 ? 'review' : 'reviews'}
            </span>
          </div>

          <div className="text-2xl font-bold text-blue-600 mb-4">
            ₹{product.price.toFixed(2)}
          </div>

          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2">Description</h2>
            <p className="text-gray-700">{product.description}</p>
          </div>

          <div className="border-t border-gray-200 pt-4">
            <div className="flex items-center justify-between mb-4">
              <div>Status:</div>
              <div>
                {product.stock > 0 ? (
                  <span className="text-green-600 font-medium">In Stock</span>
                ) : (
                  <span className="text-red-600 font-medium">Out of Stock</span>
                )}
              </div>
            </div>

            {product.stock > 0 && (
              <div className="flex items-center justify-between mb-4">
                <div>Quantity:</div>
                <select
                  value={qty}
                  onChange={(e) => setQty(Number(e.target.value))}
                  className="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {[...Array(Math.min(product.stock, 10)).keys()].map(
                    (x) => (
                      <option key={x + 1} value={x + 1}>
                        {x + 1}
                      </option>
                    )
                  )}
                </select>
              </div>
            )}

            <button
              onClick={addToCartHandler}
              disabled={product.stock === 0}
              className={`w-full py-3 px-4 rounded-md font-medium text-white ${
                product.stock === 0
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700'
              }`}
            >
              {product.stock === 0 ? 'Out of Stock' : 'Add to Cart'}
            </button>
          </div>
        </div>
      </div>

      <div className="mt-12">
        <h2 className="text-2xl font-bold mb-4">Reviews</h2>
        {product.reviews && product.reviews.length === 0 ? (
          <div className="bg-gray-100 p-4 rounded">No reviews yet</div>
        ) : (
          <div className="space-y-4">
            {product.reviews &&
              product.reviews.map((review) => (
                <div
                  key={review._id}
                  className="bg-white p-4 rounded-lg shadow-sm"
                >
                  <div className="flex justify-between mb-2">
                    <div className="font-medium">{review.name}</div>
                    <div className="text-gray-500 text-sm">
                      {new Date(review.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                  <div className="flex mb-2">
                    {renderRatingStars(review.rating)}
                  </div>
                  <p className="text-gray-700">{review.comment}</p>
                </div>
              ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductDetail;
