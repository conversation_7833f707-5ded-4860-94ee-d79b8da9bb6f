import { Navigate, Outlet } from 'react-router-dom';
import { useSelector } from 'react-redux';

const ProtectedRoute = ({ allowedRoles }) => {
  const { user } = useSelector((state) => state.auth);

  // Debug: Log user and role information
  console.log('ProtectedRoute - User:', user);
  console.log('ProtectedRoute - Allowed Roles:', allowedRoles);

  // Check if user is authenticated
  if (!user) {
    console.log('ProtectedRoute - No user, redirecting to login');
    return <Navigate to="/login" replace />;
  }

  // Check if user has required role
  if (allowedRoles) {
    // Extract role from user object
    const userRole = user.role || (user.data && user.data.role);
    const isUserAdmin = userRole === 'admin' || user.isAdmin === true;

    console.log('ProtectedRoute - User role:', userRole);
    console.log('ProtectedRoute - Is admin:', isUserAdmin);

    // Check for both role and isAdmin property for backward compatibility
    const hasRequiredRole =
      allowedRoles.includes(userRole) ||
      (allowedRoles.includes('admin') && isUserAdmin);

    if (!hasRequiredRole) {
      console.log('ProtectedRoute - User does not have required role, redirecting to unauthorized');
      return <Navigate to="/unauthorized" replace />;
    }
  }

  return <Outlet />;
};

export default ProtectedRoute;
