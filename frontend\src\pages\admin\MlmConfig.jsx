import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { FaSave, FaUndo } from 'react-icons/fa';
import { getMlmConfig, updateMlmConfig, reset } from '../../features/mlm/mlmSlice';
import Spinner from '../../components/Spinner';

const MlmConfig = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { mlmConfig, isLoading, isError, isSuccess, message } = useSelector(
    (state) => state.mlm
  );
  const { user } = useSelector((state) => state.auth);

  const [formData, setFormData] = useState({
    pairCommissionRate: 0.1,
    maxPairCommissionPerDay: 1000,
    directReferralCommissionRate: 0.05,
    minimumWithdrawalAmount: 50,
    withdrawalFee: 2,
    pvToInrRate: 0.10,
    minPVForActivation: 500,
    autoProcessCommissions: true,
    commissionProcessingDay: 1,
  });

  useEffect(() => {
    // Debug: Log user information
    console.log('MlmConfig - User:', user);

    // Extract role from user object
    const userRole = user?.role || (user?.data && user.data.role);
    const isUserAdmin = userRole === 'admin' || user?.isAdmin === true;

    console.log('MlmConfig - User role:', userRole);
    console.log('MlmConfig - Is admin:', isUserAdmin);

    // Check for admin role using both role property and isAdmin flag
    if (!user || !isUserAdmin) {
      console.log('MlmConfig - Not an admin user, redirecting to login');
      navigate('/login');
      return;
    }

    console.log('MlmConfig - Admin user confirmed, fetching MLM config');
    dispatch(getMlmConfig());

    if (isError) {
      toast.error(message);
    }

    return () => {
      dispatch(reset());
    };
  }, [dispatch, navigate, user, isError, message]);

  useEffect(() => {
    if (mlmConfig) {
      setFormData({
        pairCommissionRate: mlmConfig.pairCommissionRate,
        maxPairCommissionPerDay: mlmConfig.maxPairCommissionPerDay,
        directReferralCommissionRate: mlmConfig.directReferralCommissionRate,
        minimumWithdrawalAmount: mlmConfig.minimumWithdrawalAmount,
        withdrawalFee: mlmConfig.withdrawalFee,
        pvToInrRate: mlmConfig.pvToInrRate || 0.10,
        minPVForActivation: mlmConfig.minPVForActivation || 500,
        autoProcessCommissions: mlmConfig.autoProcessCommissions,
        commissionProcessingDay: mlmConfig.commissionProcessingDay,
      });
    }
  }, [mlmConfig]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : type === 'number' ? Number(value) : value,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    dispatch(updateMlmConfig(formData));
  };

  const handleReset = () => {
    if (mlmConfig) {
      setFormData({
        pairCommissionRate: mlmConfig.pairCommissionRate,
        maxPairCommissionPerDay: mlmConfig.maxPairCommissionPerDay,
        directReferralCommissionRate: mlmConfig.directReferralCommissionRate,
        minimumWithdrawalAmount: mlmConfig.minimumWithdrawalAmount,
        withdrawalFee: mlmConfig.withdrawalFee,
        pvToInrRate: mlmConfig.pvToInrRate || 0.10,
        minPVForActivation: mlmConfig.minPVForActivation || 500,
        autoProcessCommissions: mlmConfig.autoProcessCommissions,
        commissionProcessingDay: mlmConfig.commissionProcessingDay,
      });
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <Spinner />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">MLM Configuration</h1>

      <div className="bg-white rounded-lg shadow-md p-6">
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label htmlFor="pairCommissionRate" className="block text-gray-700 mb-2">
                Pair Commission Rate
              </label>
              <div className="flex">
                <input
                  type="number"
                  id="pairCommissionRate"
                  name="pairCommissionRate"
                  value={formData.pairCommissionRate}
                  onChange={handleChange}
                  step="0.01"
                  min="0"
                  max="1"
                  className="w-full border border-gray-300 rounded-l-md px-3 py-2"
                  required
                />
                <span className="bg-gray-100 border border-l-0 border-gray-300 rounded-r-md px-3 py-2 text-gray-500">
                  (0-1)
                </span>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Percentage of commission for each pair match (e.g., 0.1 = 10%)
              </p>
            </div>

            <div>
              <label htmlFor="maxPairCommissionPerDay" className="block text-gray-700 mb-2">
                Max Pair Commission Per Day
              </label>
              <div className="flex">
                <span className="bg-gray-100 border border-r-0 border-gray-300 rounded-l-md px-3 py-2 text-gray-500">
                  ₹
                </span>
                <input
                  type="number"
                  id="maxPairCommissionPerDay"
                  name="maxPairCommissionPerDay"
                  value={formData.maxPairCommissionPerDay}
                  onChange={handleChange}
                  min="0"
                  className="w-full border border-gray-300 rounded-r-md px-3 py-2"
                  required
                />
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Maximum commission a user can earn from pair matches per day
              </p>
            </div>

            <div>
              <label htmlFor="directReferralCommissionRate" className="block text-gray-700 mb-2">
                Direct Referral Commission Rate
              </label>
              <div className="flex">
                <input
                  type="number"
                  id="directReferralCommissionRate"
                  name="directReferralCommissionRate"
                  value={formData.directReferralCommissionRate}
                  onChange={handleChange}
                  step="0.01"
                  min="0"
                  max="1"
                  className="w-full border border-gray-300 rounded-l-md px-3 py-2"
                  required
                />
                <span className="bg-gray-100 border border-l-0 border-gray-300 rounded-r-md px-3 py-2 text-gray-500">
                  (0-1)
                </span>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Percentage of commission for direct referrals (e.g., 0.05 = 5%)
              </p>
            </div>

            <div>
              <label htmlFor="minimumWithdrawalAmount" className="block text-gray-700 mb-2">
                Minimum Withdrawal Amount
              </label>
              <div className="flex">
                <span className="bg-gray-100 border border-r-0 border-gray-300 rounded-l-md px-3 py-2 text-gray-500">
                  ₹
                </span>
                <input
                  type="number"
                  id="minimumWithdrawalAmount"
                  name="minimumWithdrawalAmount"
                  value={formData.minimumWithdrawalAmount}
                  onChange={handleChange}
                  min="0"
                  className="w-full border border-gray-300 rounded-r-md px-3 py-2"
                  required
                />
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Minimum amount a user can withdraw from their wallet
              </p>
            </div>

            <div>
              <label htmlFor="withdrawalFee" className="block text-gray-700 mb-2">
                Withdrawal Fee
              </label>
              <div className="flex">
                <span className="bg-gray-100 border border-r-0 border-gray-300 rounded-l-md px-3 py-2 text-gray-500">
                  ₹
                </span>
                <input
                  type="number"
                  id="withdrawalFee"
                  name="withdrawalFee"
                  value={formData.withdrawalFee}
                  onChange={handleChange}
                  min="0"
                  className="w-full border border-gray-300 rounded-r-md px-3 py-2"
                  required
                />
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Fee charged for each withdrawal
              </p>
            </div>

            <div>
              <label htmlFor="pvToInrRate" className="block text-gray-700 mb-2">
                PV to INR Conversion Rate
              </label>
              <div className="flex">
                <input
                  type="number"
                  id="pvToInrRate"
                  name="pvToInrRate"
                  value={formData.pvToInrRate}
                  onChange={handleChange}
                  step="0.01"
                  min="0.01"
                  className="w-full border border-gray-300 rounded-l-md px-3 py-2"
                  required
                />
                <span className="bg-gray-100 border border-l-0 border-gray-300 rounded-r-md px-3 py-2 text-gray-500">
                  ₹/PV
                </span>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Value of 1 PV in Indian Rupees (e.g., 0.10 = ₹0.10 per PV)
              </p>
            </div>

            <div>
              <label htmlFor="minPVForActivation" className="block text-gray-700 mb-2">
                Minimum PV for ID Activation
              </label>
              <input
                type="number"
                id="minPVForActivation"
                name="minPVForActivation"
                value={formData.minPVForActivation}
                onChange={handleChange}
                min="1"
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                required
              />
              <p className="text-sm text-gray-500 mt-1">
                Minimum personal PV required to activate a user ID
              </p>
            </div>

            <div>
              <label htmlFor="commissionProcessingDay" className="block text-gray-700 mb-2">
                Commission Processing Day
              </label>
              <select
                id="commissionProcessingDay"
                name="commissionProcessingDay"
                value={formData.commissionProcessingDay}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                required
              >
                {[...Array(28).keys()].map((day) => (
                  <option key={day + 1} value={day + 1}>
                    {day + 1}
                  </option>
                ))}
              </select>
              <p className="text-sm text-gray-500 mt-1">
                Day of the month when commissions are processed automatically
              </p>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="autoProcessCommissions"
                name="autoProcessCommissions"
                checked={formData.autoProcessCommissions}
                onChange={handleChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="autoProcessCommissions" className="ml-2 block text-gray-700">
                Auto Process Commissions
              </label>
              <p className="text-sm text-gray-500 mt-1 ml-6">
                Automatically process commissions on the specified day
              </p>
            </div>
          </div>

          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={handleReset}
              className="bg-gray-300 hover:bg-gray-400 text-gray-800 py-2 px-4 rounded-md flex items-center"
            >
              <FaUndo className="mr-2" /> Reset
            </button>
            <button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center"
            >
              <FaSave className="mr-2" /> Save Changes
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default MlmConfig;
