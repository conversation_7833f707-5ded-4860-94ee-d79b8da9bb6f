import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { FaSpinner } from 'react-icons/fa';
import { getOrderDetails, payOrder, reset } from '../features/orders/orderSlice';
import Spinner from '../components/Spinner';

const OrderPay = () => {
  const { id: orderId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [sdkReady, setSdkReady] = useState(false);

  const { order, isLoading, isSuccess, isError, message } = useSelector(
    (state) => state.orders
  );
  const { user } = useSelector((state) => state.auth);

  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }

    // Fetch order details
    dispatch(getOrderDetails(orderId));

    // Load Razorpay SDK
    const loadRazorpayScript = () => {
      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.async = true;
      script.onload = () => {
        setSdkReady(true);
      };
      document.body.appendChild(script);
    };

    if (!window.Razorpay) {
      loadRazorpayScript();
    } else {
      setSdkReady(true);
    }

    if (isError) {
      toast.error(message);
    }

    // If payment is successful, redirect to order details
    if (isSuccess && order && order.isPaid) {
      navigate(`/order/${orderId}`);
    }

    return () => {
      dispatch(reset());
    };
  }, [dispatch, orderId, isSuccess, isError, message, navigate, user, order]);

  const handlePayment = () => {
    if (!order) return;

    const options = {
      key: 'rzp_test_YOUR_KEY_HERE', // Replace with your Razorpay key
      amount: order.totalPrice * 100, // Amount in paisa
      currency: 'INR',
      name: 'Binary MLM E-commerce',
      description: `Order #${order._id}`,
      order_id: '', // This would come from your backend after creating a Razorpay order
      handler: function (response) {
        // Handle successful payment
        dispatch(
          payOrder({
            orderId,
            paymentResult: {
              id: response.razorpay_payment_id,
              status: 'completed',
              update_time: Date.now(),
              email_address: user.email,
            },
          })
        );
      },
      prefill: {
        name: user.name,
        email: user.email,
        contact: order.shippingAddress.phone,
      },
      theme: {
        color: '#2563EB', // Blue color
      },
    };

    const razorpayInstance = new window.Razorpay(options);
    razorpayInstance.open();
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-lg mx-auto">
        <h1 className="text-2xl font-bold mb-6">Order Payment</h1>

        {isLoading ? (
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <Spinner />
            <p className="mt-4 text-gray-600">Loading order details...</p>
          </div>
        ) : isError ? (
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <p className="text-red-500">{message}</p>
          </div>
        ) : !order ? (
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <p className="text-gray-600">Order not found</p>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-2">Order Summary</h2>
              <p className="text-gray-700">
                <strong>Order ID:</strong> {order._id}
              </p>
              <p className="text-gray-700">
                <strong>Total Amount:</strong> ₹{order.totalPrice}
              </p>
              <p className="text-gray-700">
                <strong>Total PV:</strong> {order.totalPointValue}
              </p>
            </div>

            {order.isPaid ? (
              <div className="bg-green-100 text-green-800 p-4 rounded-md mb-6">
                <p className="font-semibold">Payment Successful!</p>
                <p>Paid on {new Date(order.paidAt).toLocaleString()}</p>
              </div>
            ) : (
              <div className="mb-6">
                <h2 className="text-lg font-semibold mb-4">Complete Your Payment</h2>

                {!sdkReady ? (
                  <div className="text-center py-4">
                    <FaSpinner className="animate-spin text-blue-600 text-2xl mx-auto" />
                    <p className="mt-2 text-gray-600">Loading payment gateway...</p>
                  </div>
                ) : (
                  <button
                    onClick={handlePayment}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-md font-semibold"
                  >
                    Pay Now
                  </button>
                )}
              </div>
            )}

            <div className="mt-4 text-center">
              <button
                onClick={() => navigate(`/order/${orderId}`)}
                className="text-blue-600 hover:text-blue-800"
              >
                Back to Order Details
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderPay;
