import { Link, useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { FaStar, FaShoppingCart } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { addToCart } from '../features/cart/cartSlice';
import { getProductImageUrl } from '../utils/imageUtils';

const ProductCard = ({ product }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const handleAddToCart = (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (product.stock <= 0) {
      toast.error('Product is out of stock');
      return;
    }

    // If product has variants, navigate to product detail page
    if (product.variants && product.variants.length > 0) {
      navigate(`/product/${product._id}`);
      return;
    }

    const cartItem = {
      product: product._id,
      name: product.name,
      image: getProductImageUrl(product),
      price: product.price,
      pointValue: product.pointValue,
      stock: product.stock,
      qty: 1,
      variant: null
    };

    dispatch(addToCart(cartItem));
    toast.success('Product added to cart');
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
      <Link to={`/product/${product._id}`}>
        <img
          src={getProductImageUrl(product)}
          alt={product.name}
          className="w-full h-48 object-cover"
        />
      </Link>

      <div className="p-4">
        <Link to={`/product/${product._id}`}>
          <h3 className="text-lg font-semibold text-gray-800 hover:text-blue-600 mb-1 truncate">
            {product.name}
          </h3>
        </Link>

        <div className="flex items-center mb-2">
          <div className="flex text-yellow-400 mr-1">
            {[...Array(5)].map((_, i) => (
              <FaStar
                key={i}
                className={i < Math.round(product.ratings) ? 'text-yellow-400' : 'text-gray-300'}
              />
            ))}
          </div>
          <span className="text-sm text-gray-600">({product.numReviews} reviews)</span>
        </div>

        <div className="flex justify-between items-center">
          <div>
            <p className="text-xl font-bold text-gray-800">₹{product.price.toFixed(2)}</p>
            <p className="text-sm text-green-600">PV: {product.pointValue}</p>
          </div>

          <button
            onClick={handleAddToCart}
            className={`${product.stock > 0 ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-400 cursor-not-allowed'} text-white p-2 rounded-full`}
            title={product.stock > 0 ? 'Add to Cart' : 'Out of Stock'}
            disabled={product.stock <= 0}
          >
            <FaShoppingCart />
          </button>
        </div>

        {product.stock <= 0 && (
          <div className="mt-2">
            <span className="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">
              Out of Stock
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductCard;
