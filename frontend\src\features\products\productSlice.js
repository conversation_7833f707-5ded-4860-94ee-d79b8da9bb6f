import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../utils/api';

const initialState = {
  products: [],
  product: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: '',
  page: 1,
  pages: 1,
  count: 0,
};

// Get all products
export const getProducts = createAsyncThunk(
  'products/getAll',
  async (params, thunkAPI) => {
    try {
      const { page = 1, limit = 12, category = '', search = '', sort = '' } = params || {};

      let queryString = `?page=${page}&limit=${limit}`;
      if (category) queryString += `&category=${category}`;
      if (search) queryString += `&name=${search}`;
      if (sort) queryString += `&sort=${sort}`;

      const response = await api.get(`/products${queryString}`);
      return response.data;
    } catch (error) {
      const message =
        (error.response?.data?.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get product details
export const getProductDetails = createAsyncThunk(
  'products/getDetails',
  async (id, thunkAPI) => {
    try {
      const response = await api.get(`/products/${id}`);
      return response.data;
    } catch (error) {
      const message =
        (error.response?.data?.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Create product review
export const createProductReview = createAsyncThunk(
  'products/createReview',
  async ({ productId, review }, thunkAPI) => {
    try {
      const response = await api.post(`/products/${productId}/reviews`, review);
      return response.data;
    } catch (error) {
      const message =
        (error.response?.data?.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Delete product
export const deleteProduct = createAsyncThunk(
  'products/delete',
  async (id, thunkAPI) => {
    try {
      const token = thunkAPI.getState().auth.user.token;
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      await api.delete(`/products/${id}`, config);
      return id;
    } catch (error) {
      const message =
        (error.response?.data?.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Create product
export const createProduct = createAsyncThunk(
  'products/create',
  async (productData, thunkAPI) => {
    try {
      const token = thunkAPI.getState().auth.user.token;
      
      if (!token) {
        return thunkAPI.rejectWithValue('No authentication token found');
      }
      
      const formData = new FormData();
      
      // Add all non-image fields to FormData
      Object.keys(productData).forEach(key => {
        if (key !== 'images') {
          formData.append(key, productData[key]);
        }
      });
      
      // Handle images
      if (productData.images && Array.isArray(productData.images)) {
        productData.images.forEach(imageData => {
          if (imageData && imageData.file) {
            formData.append('images', imageData.file);
          }
        });
      }

      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${token}`
        }
      };

      const response = await api.post('/products', formData, config);
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || error.toString();
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Update product
export const updateProduct = createAsyncThunk(
  'products/update',
  async ({ id, productData }, thunkAPI) => {
    try {
      console.log('Redux: Updating product with ID:', id);
      console.log('Redux: Update data:', productData);
      
      const token = thunkAPI.getState().auth.user.token;
      
      if (!token) {
        return thunkAPI.rejectWithValue('No authentication token found');
      }
      
      // Check if we have new files to upload
      const hasNewFiles = productData.images && 
                         Array.isArray(productData.images) &&
                         productData.images.some(img => img.file);
      
      let config, data;
      
      if (hasNewFiles) {
        console.log('Redux: Update includes new files, using FormData');
        // Use FormData for file uploads
        const formData = new FormData();
        
        Object.keys(productData).forEach(key => {
          if (key !== 'images') {
            formData.append(key, productData[key]);
          }
        });
        
        productData.images.forEach((imageData, index) => {
          if (imageData && imageData.file) {
            console.log(`Redux: Adding update image ${index}:`, imageData.file.name);
            formData.append('images', imageData.file);
          }
        });
        
        config = {
          headers: {
            Authorization: `Bearer ${token}`,
            // Don't set Content-Type for FormData
          },
        };
        data = formData;
      } else {
        console.log('Redux: Update without new files, using JSON');
        // Regular JSON update (no new files)
        config = {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        };
        data = productData;
      }

      const response = await api.put(`/products/${id}`, data, config);
      console.log('Redux: Product updated successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Redux: Product update error:', error);
      
      const message =
        (error.response?.data?.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Upload product images (separate endpoint)
export const uploadProductImages = createAsyncThunk(
  'products/uploadImages',
  async ({ productId, images }, thunkAPI) => {
    try {
      console.log('Redux: Uploading images for product:', productId);
      
      const token = thunkAPI.getState().auth.user.token;
      
      if (!token) {
        return thunkAPI.rejectWithValue('No authentication token found');
      }
      
      const formData = new FormData();
      
      if (images && Array.isArray(images)) {
        images.forEach((imageData, index) => {
          if (imageData && imageData.file) {
            console.log(`Redux: Adding upload image ${index}:`, imageData.file.name);
            formData.append('images', imageData.file);
          }
        });
      }
      
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      const response = await api.put(`/products/${productId}/upload-images`, formData, config);
      console.log('Redux: Images uploaded successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Redux: Image upload error:', error);
      
      const message =
        (error.response?.data?.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const productSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = '';
    },
    clearProductState: (state) => {
      state.product = null;
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = '';
    },
    // Add a reducer to clear errors
    clearError: (state) => {
      state.isError = false;
      state.message = '';
    },
  },
  extraReducers: (builder) => {
    builder
      // Get Products
      .addCase(getProducts.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.message = '';
      })
      .addCase(getProducts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.products = action.payload.data || [];
        state.page = action.payload.pagination?.page || 1;
        state.pages = Math.ceil((action.payload.count || 0) / 12) || 1;
        state.count = action.payload.count || 0;
      })
      .addCase(getProducts.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        state.products = [];
      })
      
      // Get Product Details
      .addCase(getProductDetails.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.message = '';
      })
      .addCase(getProductDetails.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.product = action.payload.data;
      })
      .addCase(getProductDetails.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        state.product = null;
      })
      
      // Create Product Review
      .addCase(createProductReview.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.message = '';
      })
      .addCase(createProductReview.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        if (state.product) {
          state.product.reviews = action.payload.data.reviews;
          state.product.ratings = action.payload.data.ratings;
          state.product.numReviews = action.payload.data.numReviews;
        }
      })
      .addCase(createProductReview.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      
      // Delete Product
      .addCase(deleteProduct.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.message = '';
      })
      .addCase(deleteProduct.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.products = state.products.filter(
          (product) => product._id !== action.payload
        );
      })
      .addCase(deleteProduct.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      
      // Create Product
      .addCase(createProduct.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.isSuccess = false;
        state.message = '';
      })
      .addCase(createProduct.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        if (action.payload.data) {
          state.products.push(action.payload.data);
          state.product = action.payload.data;
        }
      })
      .addCase(createProduct.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.isSuccess = false;
        state.message = action.payload;
      })
      
      // Update Product
      .addCase(updateProduct.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.message = '';
      })
      .addCase(updateProduct.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        const index = state.products.findIndex(
          (product) => product._id === action.payload.data._id
        );
        if (index !== -1) {
          state.products[index] = action.payload.data;
        }
        state.product = action.payload.data;
      })
      .addCase(updateProduct.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      
      // Upload Product Images
      .addCase(uploadProductImages.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.message = '';
      })
      .addCase(uploadProductImages.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        if (action.payload.data) {
          const index = state.products.findIndex(
            (product) => product._id === action.payload.data._id
          );
          if (index !== -1) {
            state.products[index] = action.payload.data;
          }
          state.product = action.payload.data;
        }
      })
      .addCase(uploadProductImages.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      });
  },
});

export const { reset, clearProductState, clearError } = productSlice.actions;
export default productSlice.reducer;