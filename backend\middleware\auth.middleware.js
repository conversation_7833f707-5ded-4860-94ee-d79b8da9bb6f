const jwt = require('jsonwebtoken');
const User = require('../models/user.model');

// Protect routes - Authentication middleware
exports.protect = async (req, res, next) => {
  try {
    let token;
    console.log('Protect middleware - Headers:', req.headers.authorization);
    console.log('Protect middleware - Cookies:', req.cookies);

    // Get token from cookies or authorization header
    if (req.cookies.token) {
      token = req.cookies.token;
      console.log('Protect middleware - Token from cookies:', token);
    } else if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
      console.log('Protect middleware - Token from authorization header:', token);
    }

    // Check if token exists
    if (!token) {
      console.log('Protect middleware - No token found');
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route'
      });
    }

    try {
      // Verify token
      console.log('Protect middleware - Verifying token');
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      console.log('Protect middleware - Decoded token:', decoded);

      // Get user from the token
      const user = await User.findById(decoded.id);
      console.log('Protect middleware - User found:', user ? user._id : 'None');

      if (!user) {
        console.log('Protect middleware - User not found');
        return res.status(401).json({
          success: false,
          message: 'User not found'
        });
      }

      // Set user in request object with role from token
      req.user = user;

      // Ensure the role is set correctly
      if (decoded.role) {
        console.log('Protect middleware - Using role from token:', decoded.role);
        req.user.role = decoded.role;
      }

      console.log('Protect middleware - User role:', req.user.role);

      next();
    } catch (error) {
      console.log('Protect middleware - Token verification error:', error.message);
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route'
      });
    }
  } catch (error) {
    console.log('Protect middleware - Unexpected error:', error.message);
    next(error);
  }
};

// Role-based authorization middleware
exports.authorize = (...roles) => {
  return (req, res, next) => {
    console.log('Authorize middleware - User:', req.user);
    console.log('Authorize middleware - Allowed roles:', roles);

    // Check if user has one of the allowed roles
    // Try multiple possible locations for the role
    let userRole = null;

    // Option 1: Direct role property
    if (req.user && req.user.role) {
      userRole = req.user.role;
      console.log('Authorize middleware - Found role in req.user.role:', userRole);
    }
    // Option 2: In data object
    else if (req.user && req.user.data && req.user.data.role) {
      userRole = req.user.data.role;
      console.log('Authorize middleware - Found role in req.user.data.role:', userRole);
    }
    // Option 3: Check if admin flag is set
    else if (req.user && req.user.isAdmin === true) {
      userRole = 'admin';
      console.log('Authorize middleware - User has isAdmin flag, setting role to admin');
    }
    // Option 4: Default to user role if nothing else is found
    else {
      userRole = 'user';
      console.log('Authorize middleware - No role found, defaulting to user');
    }

    // Check if the role is allowed
    if (!roles.includes(userRole)) {
      console.log('Authorize middleware - Access denied');
      return res.status(403).json({
        success: false,
        message: `Role ${userRole} is not authorized to access this route`
      });
    }

    console.log('Authorize middleware - Access granted');
    next();
  };
};

// Verify referral code middleware
exports.verifyReferralCode = async (req, res, next) => {
  try {
    const { referralCode, position } = req.body;

    // Check if referral code exists
    if (!referralCode) {
      return res.status(400).json({
        success: false,
        message: 'Referral code is required'
      });
    }

    // Check if position is valid
    if (!position || !['left', 'right'].includes(position)) {
      return res.status(400).json({
        success: false,
        message: 'Valid position (left or right) is required'
      });
    }

    // Find referrer by referral code
    const referrer = await User.findOne({ referralCode });

    if (!referrer) {
      return res.status(400).json({
        success: false,
        message: 'Invalid referral code'
      });
    }

    // Check if the position is already occupied
    if (position === 'left' && referrer.leftLeg) {
      return res.status(400).json({
        success: false,
        message: 'Left position is already occupied'
      });
    }

    if (position === 'right' && referrer.rightLeg) {
      return res.status(400).json({
        success: false,
        message: 'Right position is already occupied'
      });
    }

    // Attach referrer to request object
    req.referrer = referrer;
    next();
  } catch (error) {
    next(error);
  }
};
