import { createSlice } from '@reduxjs/toolkit';

// Get cart from localStorage
const cartItemsFromStorage = localStorage.getItem('cartItems')
  ? JSON.parse(localStorage.getItem('cartItems'))
  : [];

const shippingAddressFromStorage = localStorage.getItem('shippingAddress')
  ? JSON.parse(localStorage.getItem('shippingAddress'))
  : {};

const initialState = {
  cartItems: cartItemsFromStorage,
  shippingAddress: shippingAddressFromStorage,
  paymentMethod: 'razorpay',
};

export const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    addToCart: (state, action) => {
      const item = action.payload;
      
      const existItem = state.cartItems.find(
        (x) => x.product === item.product && 
               (x.variant?.name === item.variant?.name && 
                x.variant?.value === item.variant?.value)
      );
      
      if (existItem) {
        state.cartItems = state.cartItems.map((x) =>
          x.product === existItem.product && 
          (x.variant?.name === existItem.variant?.name && 
           x.variant?.value === existItem.variant?.value)
            ? item
            : x
        );
      } else {
        state.cartItems = [...state.cartItems, item];
      }
      
      // Save to localStorage
      localStorage.setItem('cartItems', JSON.stringify(state.cartItems));
    },
    removeFromCart: (state, action) => {
      const { productId, variant } = action.payload;
      
      state.cartItems = state.cartItems.filter(
        (x) => !(x.product === productId && 
                (variant ? 
                  (x.variant?.name === variant.name && 
                   x.variant?.value === variant.value) : 
                  true))
      );
      
      // Save to localStorage
      localStorage.setItem('cartItems', JSON.stringify(state.cartItems));
    },
    saveShippingAddress: (state, action) => {
      state.shippingAddress = action.payload;
      
      // Save to localStorage
      localStorage.setItem('shippingAddress', JSON.stringify(action.payload));
    },
    savePaymentMethod: (state, action) => {
      state.paymentMethod = action.payload;
      
      // Save to localStorage
      localStorage.setItem('paymentMethod', JSON.stringify(action.payload));
    },
    clearCart: (state) => {
      state.cartItems = [];
      
      // Save to localStorage
      localStorage.removeItem('cartItems');
    },
  },
});

export const {
  addToCart,
  removeFromCart,
  saveShippingAddress,
  savePaymentMethod,
  clearCart,
} = cartSlice.actions;

export default cartSlice.reducer;
