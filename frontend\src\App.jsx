import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Provider } from 'react-redux';
import { store } from './features/store';

// Components
import Header from './components/Header';
import Footer from './components/Footer';
import ProtectedRoute from './components/ProtectedRoute';

// Pages
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import Dashboard from './pages/Dashboard';
import Profile from './pages/Profile';
import Products from './pages/Products';
import ProductDetail from './pages/ProductDetail';
import Cart from './pages/Cart';
import Shipping from './pages/Shipping';
import Payment from './pages/Payment';
import PlaceOrder from './pages/PlaceOrder';
import Order from './pages/Order';
import OrderPay from './pages/OrderPay';
import Unauthorized from './pages/Unauthorized';

// Admin Pages
import ProductList from './pages/admin/ProductList';
import ProductCreate from './pages/admin/ProductCreate';
import ProductEdit from './pages/admin/ProductEdit';
import UserList from './pages/admin/UserList';
import OrderList from './pages/admin/OrderList';
import MlmConfig from './pages/admin/MlmConfig';
import CommissionDashboard from './pages/admin/CommissionDashboard';
import UserCommissionDetail from './pages/admin/UserCommissionDetail';
import CommissionEdit from './pages/admin/CommissionEdit';
import CommissionCreate from './pages/admin/CommissionCreate';
import CommissionAuditLogs from './pages/admin/CommissionAuditLogs';

function App() {
  return (
    <Provider store={store}>
      <Router>
        <div className="flex flex-col min-h-screen">
          <Header />
          <main className="flex-grow">
            <Routes>
              {/* Public Routes */}
              <Route path="/" element={<Home />} />
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
              <Route path="/products" element={<Products />} />
              <Route path="/product/:id" element={<ProductDetail />} />
              <Route path="/cart" element={<Cart />} />
              <Route path="/unauthorized" element={<Unauthorized />} />

              {/* Protected Routes */}
              <Route element={<ProtectedRoute />}>
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/profile" element={<Profile />} />
                <Route path="/shipping" element={<Shipping />} />
                <Route path="/payment" element={<Payment />} />
                <Route path="/placeorder" element={<PlaceOrder />} />
                <Route path="/order/:id" element={<Order />} />
                <Route path="/order/:id/pay" element={<OrderPay />} />
              </Route>

              {/* Admin Routes */}
              <Route element={<ProtectedRoute allowedRoles={['admin']} />}>
                <Route path="/admin/users" element={<UserList />} />
                <Route path="/admin/products" element={<ProductList />} />
                <Route path="/admin/product/create" element={<ProductCreate />} />
                <Route path="/admin/product/:id/edit" element={<ProductEdit />} />
                <Route path="/admin/orders" element={<OrderList />} />
                <Route path="/admin/mlm-config" element={<MlmConfig />} />
                <Route path="/admin/commissions" element={<CommissionDashboard />} />
                <Route path="/admin/commissions/user/:userId" element={<UserCommissionDetail />} />
                <Route path="/admin/commissions/edit/:id" element={<CommissionEdit />} />
                <Route path="/admin/commissions/new" element={<CommissionCreate />} />
                <Route path="/admin/commissions/audit" element={<CommissionAuditLogs />} />
              </Route>

              {/* Franchise Routes */}
              <Route element={<ProtectedRoute allowedRoles={['admin', 'franchise']} />}>
                {/* Add franchise routes here */}
              </Route>
            </Routes>
          </main>
          <Footer />
        </div>
        <ToastContainer />
      </Router>
    </Provider>
  );
}

export default App;
