import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { FaArrowLeft, FaRupeeSign, FaExclamationTriangle } from 'react-icons/fa';
import Spinner from '../../components/Spinner';
import { updateCommission, reset } from '../../features/mlm/mlmSlice';
import api from '../../utils/api';

const CommissionEdit = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { isLoading, isSuccess, isError, message } = useSelector((state) => state.mlm);

  // Commission state
  const [commission, setCommission] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Form state
  const [formData, setFormData] = useState({
    amount: '',
    status: '',
    description: '',
    reason: '',
  });

  // Load commission data on component mount
  useEffect(() => {
    const fetchCommission = async () => {
      try {
        setLoading(true);
        console.log('Fetching commission with ID:', id);
        const response = await api.get(`/mlm/commissions/${id}`);
        console.log('Commission data response:', response.data);

        if (response.data && response.data.success && response.data.data) {
          setCommission(response.data.data);
          setFormData({
            amount: response.data.data.amount,
            status: response.data.data.status,
            description: response.data.data.description,
            reason: '',
          });
          setLoading(false);
        } else {
          throw new Error('Invalid response format');
        }
      } catch (error) {
        console.error('Error fetching commission:', error);
        const errorMessage = error.response?.data?.message || 'Failed to load commission data';
        setError(errorMessage);
        setLoading(false);
        toast.error(errorMessage);
      }
    };

    if (id) {
      fetchCommission();
    }
  }, [id]);

  // Handle success/error messages
  useEffect(() => {
    if (isError) {
      toast.error(message);
    }

    if (isSuccess) {
      toast.success('Commission updated successfully');
      navigate(`/admin/commissions/user/${commission.user._id}`);
    }

    dispatch(reset());
  }, [isError, isSuccess, message, dispatch, navigate, commission]);

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle amount change with validation
  const handleAmountChange = (e) => {
    const value = e.target.value;
    if (value === '' || (!isNaN(value) && parseFloat(value) >= 0)) {
      setFormData((prev) => ({ ...prev, amount: value }));
    }
  };

  // Submit form
  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate form
    if (!formData.amount || !formData.status || !formData.description || !formData.reason) {
      toast.error('Please fill in all fields');
      return;
    }

    // Convert amount to number
    const amount = parseFloat(formData.amount);
    if (isNaN(amount)) {
      toast.error('Amount must be a valid number');
      return;
    }

    // Check if status is changing from paid to non-paid
    const isStatusChangingFromPaid = commission.status === 'paid' && formData.status !== 'paid';

    // Check if amount is changing while status is paid
    const isAmountChangingWhilePaid =
      commission.status === 'paid' &&
      formData.status === 'paid' &&
      amount !== commission.amount;

    // Show confirmation if wallet will be affected
    if (isStatusChangingFromPaid || isAmountChangingWhilePaid) {
      const confirmMessage = isStatusChangingFromPaid
        ? `This will remove ₹${commission.amount} from the user's wallet. Are you sure?`
        : `This will ${amount > commission.amount ? 'add' : 'deduct'} ₹${Math.abs(amount - commission.amount)} ${amount > commission.amount ? 'to' : 'from'} the user's wallet. Are you sure?`;

      if (!window.confirm(confirmMessage)) {
        return;
      }
    }

    // Show confirmation if status is changing to paid
    if (commission.status !== 'paid' && formData.status === 'paid') {
      if (!window.confirm(`This will add ₹${amount} to the user's wallet. Are you sure?`)) {
        return;
      }
    }

    // Dispatch update action
    dispatch(
      updateCommission({
        id,
        commissionData: {
          amount,
          status: formData.status,
          description: formData.description,
          reason: formData.reason,
        },
      })
    );
  };

  // Format currency for display
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 2,
    }).format(amount);
  };

  if (loading || isLoading) {
    return <Spinner />;
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6">
          <p>{error}</p>
        </div>
        <button
          onClick={() => navigate('/admin/commissions')}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
        >
          Back to Commissions
        </button>
      </div>
    );
  }

  if (!commission) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6">
          <p>Commission not found</p>
        </div>
        <button
          onClick={() => navigate('/admin/commissions')}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
        >
          Back to Commissions
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <button
          onClick={() => navigate(`/admin/commissions/user/${commission.user._id}`)}
          className="mr-4 text-blue-600 hover:text-blue-800"
        >
          <FaArrowLeft className="inline mr-1" /> Back to User Commissions
        </button>
        <h1 className="text-2xl font-bold">Edit Commission</h1>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <h2 className="text-lg font-semibold mb-4">Commission Details</h2>
            <div className="space-y-3">
              <div>
                <span className="text-gray-600 text-sm">User:</span>
                <p className="font-medium">
                  {commission.user.name} ({commission.user.email})
                </p>
              </div>
              <div>
                <span className="text-gray-600 text-sm">Commission Type:</span>
                <p className="font-medium">{commission.type}</p>
              </div>
              <div>
                <span className="text-gray-600 text-sm">Created On:</span>
                <p className="font-medium">
                  {new Date(commission.createdAt).toLocaleDateString('en-IN', {
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                  })}
                </p>
              </div>
            </div>
          </div>

          <div>
            <h2 className="text-lg font-semibold mb-4">Current Values</h2>
            <div className="space-y-3">
              <div>
                <span className="text-gray-600 text-sm">Amount:</span>
                <p className="font-medium">
                  <FaRupeeSign className="inline text-xs" /> {formatCurrency(commission.amount)}
                </p>
              </div>
              <div>
                <span className="text-gray-600 text-sm">Status:</span>
                <p>
                  <span
                    className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      commission.status === 'paid'
                        ? 'bg-green-100 text-green-800'
                        : commission.status === 'rejected'
                        ? 'bg-red-100 text-red-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}
                  >
                    {commission.status}
                  </span>
                </p>
              </div>
              <div>
                <span className="text-gray-600 text-sm">Description:</span>
                <p className="font-medium">{commission.description}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Warning about wallet impact */}
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <FaExclamationTriangle className="h-5 w-5 text-yellow-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                <strong>Important:</strong> Changing the status to/from 'paid' or modifying the amount
                while the status is 'paid' will directly impact the user's wallet balance.
              </p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
                Amount (₹)
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaRupeeSign className="text-gray-400" />
                </div>
                <input
                  type="number"
                  id="amount"
                  name="amount"
                  value={formData.amount}
                  onChange={handleAmountChange}
                  className="w-full pl-8 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter amount"
                  step="0.01"
                  min="0"
                  required
                />
              </div>
            </div>

            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">Select Status</option>
                <option value="pending">Pending</option>
                <option value="paid">Paid</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>
          </div>

          <div className="mb-4">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <input
              type="text"
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Description (visible to user)"
              required
            />
          </div>

          <div className="mb-4">
            <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-1">
              Reason for Change
            </label>
            <textarea
              id="reason"
              name="reason"
              value={formData.reason}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Internal reason for this change (for audit purposes)"
              rows="3"
              required
            ></textarea>
          </div>

          <div className="flex justify-end space-x-2">
            <button
              type="button"
              onClick={() => navigate(`/admin/commissions/user/${commission.user._id}`)}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Update Commission
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CommissionEdit;
