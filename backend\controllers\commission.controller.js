const User = require('../models/user.model');
const Commission = require('../models/commission.model');
const CommissionAudit = require('../models/commissionAudit.model');
const { ErrorResponse } = require('../middleware/error.middleware');

// @desc    Get all commissions
// @route   GET /api/mlm/commissions
// @access  Private/Admin
exports.getAllCommissions = async (req, res, next) => {
  try {
    // Add pagination and filtering
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    // Build query based on filters
    const query = {};

    // Filter by user
    if (req.query.user) {
      query.user = req.query.user;
    }

    // Filter by commission type
    if (req.query.type) {
      query.type = req.query.type;
    }

    // Filter by status
    if (req.query.status) {
      query.status = req.query.status;
    }

    // Filter by date range
    if (req.query.startDate && req.query.endDate) {
      query.createdAt = {
        $gte: new Date(req.query.startDate),
        $lte: new Date(req.query.endDate)
      };
    } else if (req.query.startDate) {
      query.createdAt = { $gte: new Date(req.query.startDate) };
    } else if (req.query.endDate) {
      query.createdAt = { $lte: new Date(req.query.endDate) };
    }

    // Count total documents
    const total = await Commission.countDocuments(query);

    // Get commissions with pagination and populate user
    const commissions = await Commission.find(query)
      .populate('user', 'name email referralCode')
      .sort({ createdAt: -1 })
      .skip(startIndex)
      .limit(limit);

    // Calculate total pages
    const pages = Math.ceil(total / limit);

    res.status(200).json({
      success: true,
      count: commissions.length,
      total,
      pages,
      page,
      data: commissions
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single commission
// @route   GET /api/mlm/commissions/:id
// @access  Private/Admin
exports.getCommission = async (req, res, next) => {
  try {
    const commission = await Commission.findById(req.params.id)
      .populate('user', 'name email referralCode');

    if (!commission) {
      return next(new ErrorResponse(`Commission not found with id of ${req.params.id}`, 404));
    }

    res.status(200).json({
      success: true,
      data: commission
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get user commissions
// @route   GET /api/mlm/commissions/user/:userId
// @access  Private/Admin
exports.getUserCommissions = async (req, res, next) => {
  try {
    const userId = req.params.userId;

    // Verify user exists
    const user = await User.findById(userId);
    if (!user) {
      return next(new ErrorResponse(`User not found with id of ${userId}`, 404));
    }

    // Add pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    // Build query
    const query = { user: userId };

    // Filter by commission type
    if (req.query.type) {
      query.type = req.query.type;
    }

    // Filter by status
    if (req.query.status) {
      query.status = req.query.status;
    }

    // Filter by date range
    if (req.query.startDate && req.query.endDate) {
      query.createdAt = {
        $gte: new Date(req.query.startDate),
        $lte: new Date(req.query.endDate)
      };
    } else if (req.query.startDate) {
      query.createdAt = { $gte: new Date(req.query.startDate) };
    } else if (req.query.endDate) {
      query.createdAt = { $lte: new Date(req.query.endDate) };
    }

    // Count total documents
    const total = await Commission.countDocuments(query);

    // Get commissions with pagination
    const commissions = await Commission.find(query)
      .sort({ createdAt: -1 })
      .skip(startIndex)
      .limit(limit);

    // Calculate total pages
    const pages = Math.ceil(total / limit);

    // Get user wallet transactions
    const userWithWallet = await User.findById(userId).select('wallet');

    res.status(200).json({
      success: true,
      count: commissions.length,
      total,
      pages,
      page,
      data: commissions,
      wallet: userWithWallet.wallet
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create manual commission
// @route   POST /api/mlm/commissions
// @access  Private/Admin
exports.createCommission = async (req, res, next) => {
  try {
    const { userId, amount, type, description, status } = req.body;

    // Validate required fields
    if (!userId || !amount || !type || !description) {
      return next(new ErrorResponse('Please provide all required fields', 400));
    }

    // Verify user exists
    const user = await User.findById(userId);
    if (!user) {
      return next(new ErrorResponse(`User not found with id of ${userId}`, 404));
    }

    // Create commission
    const commission = await Commission.create({
      user: userId,
      amount,
      type,
      description,
      status: status || 'pending'
    });

    // If status is 'paid', update user's wallet
    if (status === 'paid') {
      // Ensure wallet exists and has a balance
      const previousBalance = user.wallet && user.wallet.balance ? user.wallet.balance : 0;
      const newBalance = previousBalance + amount;

      // Update user's wallet - use $set to ensure wallet exists
      await User.findByIdAndUpdate(userId, {
        $set: { 'wallet.balance': newBalance },
        $push: {
          'wallet.transactions': {
            amount,
            type: 'commission',
            description
          }
        }
      });

      // Create audit record
      await CommissionAudit.create({
        user: userId,
        admin: req.user._id,
        commission: commission._id,
        actionType: 'create',
        newValues: {
          amount,
          status,
          type,
          description
        },
        reason: req.body.reason || 'Manual commission creation',
        walletAdjustment: {
          amount,
          previousBalance,
          newBalance
        }
      });
    } else {
      // Create audit record without wallet adjustment
      await CommissionAudit.create({
        user: userId,
        admin: req.user._id,
        commission: commission._id,
        actionType: 'create',
        newValues: {
          amount,
          status,
          type,
          description
        },
        reason: req.body.reason || 'Manual commission creation'
      });
    }

    res.status(201).json({
      success: true,
      data: commission
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update commission
// @route   PUT /api/mlm/commissions/:id
// @access  Private/Admin
exports.updateCommission = async (req, res, next) => {
  try {
    const { amount, status, description, reason } = req.body;

    // Validate reason
    if (!reason) {
      return next(new ErrorResponse('Please provide a reason for the adjustment', 400));
    }

    // Find commission
    const commission = await Commission.findById(req.params.id);
    if (!commission) {
      return next(new ErrorResponse(`Commission not found with id of ${req.params.id}`, 404));
    }

    // Store previous values for audit
    const previousValues = {
      amount: commission.amount,
      status: commission.status,
      description: commission.description
    };

    // Prepare update data
    const updateData = {};
    if (amount !== undefined) updateData.amount = amount;
    if (status !== undefined) updateData.status = status;
    if (description !== undefined) updateData.description = description;

    // Update commission
    const updatedCommission = await Commission.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    );

    // Handle wallet updates if status changed to/from 'paid'
    const user = await User.findById(commission.user);
    let walletAdjustment = null;

    // If status changed from non-paid to paid, add to wallet
    if (commission.status !== 'paid' && status === 'paid') {
      // Ensure wallet exists and has a balance
      const previousBalance = user.wallet && user.wallet.balance ? user.wallet.balance : 0;
      const newBalance = previousBalance + (amount || commission.amount);

      // Update user's wallet - use $set to ensure wallet exists
      await User.findByIdAndUpdate(commission.user, {
        $set: { 'wallet.balance': newBalance },
        $push: {
          'wallet.transactions': {
            amount: amount || commission.amount,
            type: 'commission',
            description: description || commission.description
          }
        }
      });

      walletAdjustment = {
        amount: amount || commission.amount,
        previousBalance,
        newBalance
      };
    }
    // If status changed from paid to non-paid, subtract from wallet
    else if (commission.status === 'paid' && status !== 'paid') {
      // Ensure wallet exists and has a balance
      const previousBalance = user.wallet && user.wallet.balance ? user.wallet.balance : 0;
      const newBalance = previousBalance - commission.amount;

      // Don't allow negative balance
      if (newBalance < 0) {
        return next(new ErrorResponse('Reversal would result in negative wallet balance', 400));
      }

      // Update user's wallet - use $set to ensure wallet exists
      await User.findByIdAndUpdate(commission.user, {
        $set: { 'wallet.balance': newBalance },
        $push: {
          'wallet.transactions': {
            amount: -commission.amount,
            type: 'commission',
            description: `Reversal: ${commission.description}`
          }
        }
      });

      walletAdjustment = {
        amount: -commission.amount,
        previousBalance,
        newBalance
      };
    }
    // If amount changed but status was and remains 'paid', adjust the difference
    else if (commission.status === 'paid' && status === 'paid' && amount !== undefined && amount !== commission.amount) {
      const difference = amount - commission.amount;
      // Ensure wallet exists and has a balance
      const previousBalance = user.wallet && user.wallet.balance ? user.wallet.balance : 0;
      const newBalance = previousBalance + difference;

      // Don't allow negative balance if reducing the amount
      if (difference < 0 && newBalance < 0) {
        return next(new ErrorResponse('Adjustment would result in negative wallet balance', 400));
      }

      // Update user's wallet - use $set to ensure wallet exists
      await User.findByIdAndUpdate(commission.user, {
        $set: { 'wallet.balance': newBalance },
        $push: {
          'wallet.transactions': {
            amount: difference,
            type: 'commission',
            description: `Adjustment: ${description || commission.description}`
          }
        }
      });

      walletAdjustment = {
        amount: difference,
        previousBalance,
        newBalance
      };
    }

    // Create audit record
    await CommissionAudit.create({
      user: commission.user,
      admin: req.user._id,
      commission: commission._id,
      actionType: 'update',
      previousValues,
      newValues: {
        amount: amount || commission.amount,
        status: status || commission.status,
        description: description || commission.description
      },
      reason,
      walletAdjustment
    });

    res.status(200).json({
      success: true,
      data: updatedCommission
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get commission audit logs
// @route   GET /api/mlm/commissions/audit
// @access  Private/Admin
exports.getCommissionAuditLogs = async (req, res, next) => {
  try {
    // Add pagination and filtering
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    // Build query based on filters
    const query = {};

    // Filter by user
    if (req.query.user) {
      query.user = req.query.user;
    }

    // Filter by admin
    if (req.query.admin) {
      query.admin = req.query.admin;
    }

    // Filter by action type
    if (req.query.actionType) {
      query.actionType = req.query.actionType;
    }

    // Filter by date range
    if (req.query.startDate && req.query.endDate) {
      query.createdAt = {
        $gte: new Date(req.query.startDate),
        $lte: new Date(req.query.endDate)
      };
    } else if (req.query.startDate) {
      query.createdAt = { $gte: new Date(req.query.startDate) };
    } else if (req.query.endDate) {
      query.createdAt = { $lte: new Date(req.query.endDate) };
    }

    // Count total documents
    const total = await CommissionAudit.countDocuments(query);

    // Get audit logs with pagination and populate user and admin
    const auditLogs = await CommissionAudit.find(query)
      .populate('user', 'name email referralCode')
      .populate('admin', 'name email')
      .populate('commission')
      .sort({ createdAt: -1 })
      .skip(startIndex)
      .limit(limit);

    // Calculate total pages
    const pages = Math.ceil(total / limit);

    res.status(200).json({
      success: true,
      count: auditLogs.length,
      total,
      pages,
      page,
      data: auditLogs
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Adjust user wallet directly
// @route   POST /api/mlm/commissions/wallet-adjustment
// @access  Private/Admin
exports.adjustWallet = async (req, res, next) => {
  try {
    const { userId, amount, description, reason } = req.body;

    // Validate required fields
    if (!userId || amount === undefined || !description || !reason) {
      return next(new ErrorResponse('Please provide all required fields', 400));
    }

    // Verify user exists
    const user = await User.findById(userId);
    if (!user) {
      return next(new ErrorResponse(`User not found with id of ${userId}`, 404));
    }

    // Ensure wallet exists and has a balance
    const previousBalance = user.wallet && user.wallet.balance ? user.wallet.balance : 0;
    const newBalance = previousBalance + amount;

    // Don't allow negative balance
    if (newBalance < 0) {
      return next(new ErrorResponse('Adjustment would result in negative wallet balance', 400));
    }

    // Update user's wallet - use $set to ensure wallet exists
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      {
        $set: { 'wallet.balance': newBalance },
        $push: {
          'wallet.transactions': {
            amount,
            type: 'commission',
            description
          }
        }
      },
      { new: true }
    );

    // Create audit record
    await CommissionAudit.create({
      user: userId,
      admin: req.user._id,
      actionType: 'create',
      newValues: {
        amount,
        description
      },
      reason,
      walletAdjustment: {
        amount,
        previousBalance,
        newBalance
      }
    });

    res.status(200).json({
      success: true,
      data: {
        wallet: updatedUser.wallet,
        adjustment: {
          amount,
          previousBalance,
          newBalance
        }
      }
    });
  } catch (error) {
    next(error);
  }
};
