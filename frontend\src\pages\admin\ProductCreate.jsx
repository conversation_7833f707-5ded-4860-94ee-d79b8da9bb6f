import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { FaSave, FaArrowLeft } from 'react-icons/fa';
import { createProduct, reset, clearProductState } from '../../features/products/productSlice';
import Spinner from '../../components/Spinner';
import useForm from '../../hooks/useForm';

const ProductCreate = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { isLoading, isError, isSuccess, message } = useSelector(
    (state) => state.products
  );
  const { user } = useSelector((state) => state.auth);

  // Categories
  const categories = [
    'Electronics',
    'Clothing',
    'Health & Wellness',
    'Home & Kitchen',
    'Beauty',
    'Sports',
  ];

  // Initial form state - FIXED: Remove placeholder images
  const initialState = {
    name: '',
    description: '',
    price: '',
    pointValue: '',
    category: '',
    subcategory: '',
    stock: '',
    featured: false,
    images: [], // Empty array instead of placeholder
  };

  // Form validation - ADDED: Image validation
  const validateForm = (values) => {
    const errors = {};

    if (!values.name) {
      errors.name = 'Product name is required';
    } else if (values.name.length > 100) {
      errors.name = 'Product name cannot exceed 100 characters';
    }

    if (!values.description) {
      errors.description = 'Product description is required';
    }

    if (!values.price) {
      errors.price = 'Price is required';
    } else if (isNaN(values.price) || Number(values.price) < 0) {
      errors.price = 'Price must be a positive number';
    }

    if (!values.pointValue) {
      errors.pointValue = 'Point value is required';
    } else if (isNaN(values.pointValue) || Number(values.pointValue) < 0) {
      errors.pointValue = 'Point value must be a positive number';
    }

    if (!values.category) {
      errors.category = 'Category is required';
    }

    if (!values.stock) {
      errors.stock = 'Stock is required';
    } else if (isNaN(values.stock) || Number(values.stock) < 0) {
      errors.stock = 'Stock must be a non-negative number';
    }

    // ADDED: Image validation
    if (!values.images || values.images.length === 0) {
      errors.images = 'At least one product image is required';
    }

    return errors;
  };

  // Define onSubmit function before using it in useForm
  const onSubmit = (formValues) => {
    console.log('Form submission data:', formValues); // Debug log
    
    // Convert string values to numbers and prepare data
    const productData = {
      ...formValues,
      price: Number(formValues.price),
      pointValue: Number(formValues.pointValue),
      stock: Number(formValues.stock),
      images: formValues.images || [], // Ensure images array exists
    };

    console.log('Processed product data:', productData); // Debug log
    dispatch(createProduct(productData));
  };

  // Use custom form hook
  const { values, errors, touched, handleChange, handleSubmit } = useForm(
    initialState,
    validateForm,
    onSubmit
  );

  // FIXED: Better state management for image previews
  const [selectedFiles, setSelectedFiles] = useState([]);

  // Handle image upload with proper cleanup
  const handleImageUpload = (e) => {
    const files = Array.from(e.target.files);
    
    if (files.length === 0) return;
    
    // Clean up previous object URLs
    selectedFiles.forEach(fileData => {
      if (fileData.previewUrl) {
        URL.revokeObjectURL(fileData.previewUrl);
      }
    });
    
    // Create new file data with previews
    const newFileData = files.map(file => ({
      file,
      previewUrl: URL.createObjectURL(file),
      name: file.name
    }));
    
    setSelectedFiles(newFileData);
    
    // Update form values with proper file objects
    const formImages = newFileData.map(fileData => ({
      file: fileData.file,
      url: fileData.previewUrl
    }));
  
    handleChange({
      target: {
        name: 'images',
        value: formImages
      }
    });
  };
  
  // Clean up on unmount
  useEffect(() => {
    return () => {
      selectedFiles.forEach(fileData => {
        if (fileData.previewUrl) {
          URL.revokeObjectURL(fileData.previewUrl);
        }
      });
    };
  }, [selectedFiles]);

  // Handle success/error messages and redirection
  useEffect(() => {
    if (isError) {
      toast.error(message);
    }

    if (isSuccess) {
      toast.success('Product created successfully');
      // Clean up file URLs before navigation
      selectedFiles.forEach(fileData => {
        if (fileData.previewUrl) {
          URL.revokeObjectURL(fileData.previewUrl);
        }
      });
      dispatch(clearProductState());
      navigate('/admin/products');
    }

    return () => {
      dispatch(clearProductState());
    };
  }, [isError, isSuccess, message, navigate, dispatch, selectedFiles]);

  // Show loading spinner
  if (isLoading) {
    return <Spinner />;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Create Product</h1>
        <button
          onClick={() => navigate('/admin/products')}
          className="bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md flex items-center"
        >
          <FaArrowLeft className="mr-2" /> Back to Products
        </button>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Product Name */}
            <div className="col-span-2">
              <label className="block text-gray-700 font-medium mb-2" htmlFor="name">
                Product Name*
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={values.name}
                onChange={handleChange}
                className={`w-full border ${
                  errors.name && touched.name ? 'border-red-500' : 'border-gray-300'
                } rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
              {errors.name && touched.name && (
                <p className="text-red-500 text-sm mt-1">{errors.name}</p>
              )}
            </div>

            {/* Price */}
            <div>
              <label className="block text-gray-700 font-medium mb-2" htmlFor="price">
                Price (₹)*
              </label>
              <input
                type="number"
                id="price"
                name="price"
                min="0"
                step="0.01"
                value={values.price}
                onChange={handleChange}
                className={`w-full border ${
                  errors.price && touched.price ? 'border-red-500' : 'border-gray-300'
                } rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
              {errors.price && touched.price && (
                <p className="text-red-500 text-sm mt-1">{errors.price}</p>
              )}
            </div>

            {/* Point Value */}
            <div>
              <label className="block text-gray-700 font-medium mb-2" htmlFor="pointValue">
                Point Value (PV)*
              </label>
              <input
                type="number"
                id="pointValue"
                name="pointValue"
                min="0"
                value={values.pointValue}
                onChange={handleChange}
                className={`w-full border ${
                  errors.pointValue && touched.pointValue ? 'border-red-500' : 'border-gray-300'
                } rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
              {errors.pointValue && touched.pointValue && (
                <p className="text-red-500 text-sm mt-1">{errors.pointValue}</p>
              )}
            </div>

            {/* Category */}
            <div>
              <label className="block text-gray-700 font-medium mb-2" htmlFor="category">
                Category*
              </label>
              <select
                id="category"
                name="category"
                value={values.category}
                onChange={handleChange}
                className={`w-full border ${
                  errors.category && touched.category ? 'border-red-500' : 'border-gray-300'
                } rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
              >
                <option value="">Select Category</option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
              {errors.category && touched.category && (
                <p className="text-red-500 text-sm mt-1">{errors.category}</p>
              )}
            </div>

            {/* Subcategory */}
            <div>
              <label className="block text-gray-700 font-medium mb-2" htmlFor="subcategory">
                Subcategory
              </label>
              <input
                type="text"
                id="subcategory"
                name="subcategory"
                value={values.subcategory}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Stock */}
            <div>
              <label className="block text-gray-700 font-medium mb-2" htmlFor="stock">
                Stock*
              </label>
              <input
                type="number"
                id="stock"
                name="stock"
                min="0"
                value={values.stock}
                onChange={handleChange}
                className={`w-full border ${
                  errors.stock && touched.stock ? 'border-red-500' : 'border-gray-300'
                } rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
              {errors.stock && touched.stock && (
                <p className="text-red-500 text-sm mt-1">{errors.stock}</p>
              )}
            </div>

            {/* Featured */}
            <div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="featured"
                  name="featured"
                  checked={values.featured}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="ml-2 block text-gray-700 font-medium" htmlFor="featured">
                  Featured Product
                </label>
              </div>
            </div>

            {/* Description */}
            <div className="col-span-2">
              <label className="block text-gray-700 font-medium mb-2" htmlFor="description">
                Description*
              </label>
              <textarea
                id="description"
                name="description"
                rows="4"
                value={values.description}
                onChange={handleChange}
                className={`w-full border ${
                  errors.description && touched.description ? 'border-red-500' : 'border-gray-300'
                } rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
              ></textarea>
              {errors.description && touched.description && (
                <p className="text-red-500 text-sm mt-1">{errors.description}</p>
              )}
            </div>

            {/* FIXED: Image Upload */}
            <div className="col-span-2">
              <label className="block text-gray-700 font-medium mb-2">
                Product Images* (Max 5 images)
              </label>
              <div className={`border border-dashed ${
                errors.images && touched.images ? 'border-red-500' : 'border-gray-300'
              } rounded-md p-4`}>
                <div className="flex flex-col items-center justify-center">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    id="image-upload"
                    max="5"
                  />
                  <label
                    htmlFor="image-upload"
                    className="bg-blue-50 hover:bg-blue-100 text-blue-600 py-2 px-4 rounded-md cursor-pointer"
                  >
                    Choose Images (Max 5)
                  </label>
                  <p className="text-sm text-gray-500 mt-2">
                    Supported formats: JPG, PNG, GIF. Max size: 5MB per image
                  </p>
                  
                  {/* FIXED: Image Previews */}
                  {selectedFiles.length > 0 && (
                    <div className="mt-4 w-full">
                      <p className="text-sm font-medium text-gray-700 mb-2">
                        Selected Images ({selectedFiles.length}/5):
                      </p>
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                        {selectedFiles.map((fileData, index) => (
                          <div key={index} className="relative group">
                            <img
                              src={fileData.previewUrl}
                              alt={`Preview ${index + 1}`}
                              className="w-full h-24 object-cover rounded-md border"
                            />
                            <button
                              type="button"
                              onClick={() => removeImage(index)}
                              className="absolute -top-2 -right-2 bg-red-500 hover:bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm opacity-0 group-hover:opacity-100 transition-opacity"
                            >
                              ×
                            </button>
                            <p className="text-xs text-gray-500 mt-1 truncate">
                              {fileData.name}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
              {errors.images && touched.images && (
                <p className="text-red-500 text-sm mt-1">{errors.images}</p>
              )}
            </div>
          </div>

          <div className="mt-8 flex justify-end">
            <button
              type="submit"
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-2 px-6 rounded-md flex items-center"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  <FaSave className="mr-2" /> Create Product
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProductCreate;