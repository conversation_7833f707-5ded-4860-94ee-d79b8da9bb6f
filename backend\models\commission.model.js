const mongoose = require('mongoose');

const commissionSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  amount: {
    type: Number,
    required: true
  },
  type: {
    type: String,
    enum: ['binary', 'referral', 'leadership', 'bonus', 'direct', 'matching', 'manual'],
    required: true
  },
  description: {
    type: String,
    required: true
  },
  // For binary commissions
  leftPV: {
    type: Number
  },
  rightPV: {
    type: Number
  },
  // PV carried forward after commission calculation
  leftCarryForward: {
    type: Number,
    default: 0
  },
  rightCarryForward: {
    type: Number,
    default: 0
  },
  // Related order if applicable
  order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order'
  },
  // Status of commission
  status: {
    type: String,
    enum: ['pending', 'paid', 'cancelled', 'rejected'],
    default: 'pending'
  },
  paidAt: {
    type: Date
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('Commission', commissionSchema);
