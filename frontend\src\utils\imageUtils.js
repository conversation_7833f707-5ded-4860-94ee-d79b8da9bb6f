// Utility functions for handling product images

const API_BASE_URL = import.meta.env.VITE_API_URL?.replace('/api', '') || 'http://localhost:5000';

/**
 * Constructs a full image URL from a relative path
 * @param {string} imagePath - The relative image path (e.g., "/uploads/products/image.jpg")
 * @returns {string} - The full image URL
 */
export const getImageUrl = (imagePath) => {
  if (!imagePath) {
    return 'https://via.placeholder.com/300';
  }
  
  // If it's already a full URL, return as is
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }
  
  // If it's a relative path, construct the full URL
  return `${API_BASE_URL}${imagePath}`;
};

/**
 * Gets the first image URL from a product's images array
 * @param {Object} product - The product object
 * @returns {string} - The first image URL or placeholder
 */
export const getProductImageUrl = (product) => {
  if (!product || !product.images || product.images.length === 0) {
    return 'https://via.placeholder.com/300';
  }
  
  return getImageUrl(product.images[0].url);
};

/**
 * Gets all image URLs from a product's images array
 * @param {Object} product - The product object
 * @returns {Array} - Array of image URLs
 */
export const getProductImageUrls = (product) => {
  if (!product || !product.images || product.images.length === 0) {
    return ['https://via.placeholder.com/300'];
  }
  
  return product.images.map(image => getImageUrl(image.url));
};
