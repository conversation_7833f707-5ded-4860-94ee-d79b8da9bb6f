import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { savePaymentMethod } from '../features/cart/cartSlice';
import { toast } from 'react-toastify';
import { FaCreditCard, FaWallet, FaMoneyBillWave } from 'react-icons/fa';
import CheckoutSteps from '../components/CheckoutSteps';

const Payment = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  
  const { shippingAddress, paymentMethod: savedPaymentMethod } = useSelector((state) => state.cart);
  const { user } = useSelector((state) => state.auth);
  
  const [paymentMethod, setPaymentMethod] = useState(savedPaymentMethod || 'razorpay');
  
  useEffect(() => {
    if (!user) {
      navigate('/login?redirect=payment');
      return;
    }
    
    if (!shippingAddress.address) {
      toast.error('Shipping address is required');
      navigate('/shipping');
    }
  }, [user, shippingAddress, navigate]);
  
  const handleSubmit = (e) => {
    e.preventDefault();
    
    dispatch(savePaymentMethod(paymentMethod));
    navigate('/placeorder');
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <CheckoutSteps step1 step2 step3 />
      
      <div className="max-w-lg mx-auto">
        <h1 className="text-2xl font-bold mb-6">Payment Method</h1>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <form onSubmit={handleSubmit}>
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-4">Select Payment Method</h2>
              
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="radio"
                    id="razorpay"
                    name="paymentMethod"
                    value="razorpay"
                    checked={paymentMethod === 'razorpay'}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="razorpay" className="ml-3 flex items-center">
                    <FaCreditCard className="text-blue-600 mr-2" />
                    <span>Razorpay (Credit/Debit Card, UPI, Netbanking)</span>
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="radio"
                    id="wallet"
                    name="paymentMethod"
                    value="wallet"
                    checked={paymentMethod === 'wallet'}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="wallet" className="ml-3 flex items-center">
                    <FaWallet className="text-green-600 mr-2" />
                    <span>Wallet Balance</span>
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="radio"
                    id="cod"
                    name="paymentMethod"
                    value="cod"
                    checked={paymentMethod === 'cod'}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="cod" className="ml-3 flex items-center">
                    <FaMoneyBillWave className="text-gray-600 mr-2" />
                    <span>Cash on Delivery</span>
                  </label>
                </div>
              </div>
            </div>
            
            <button
              type="submit"
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-md font-semibold"
            >
              Continue to Review Order
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Payment;
