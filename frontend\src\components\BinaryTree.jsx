import { useCallback } from 'react';
import Tree from 'react-d3-tree';
import { FaUser } from 'react-icons/fa';

const BinaryTree = ({ data }) => {
  // Custom node renderer
  const renderCustomNodeElement = useCallback(
    ({ nodeDatum, toggleNode }) => (
      <g>
        <circle
          r={20}
          fill={nodeDatum.leftPV && nodeDatum.rightPV ? '#4CAF50' : '#2196F3'}
          onClick={toggleNode}
        />
        <foreignObject
          width={30}
          height={30}
          x={-15}
          y={-15}
          style={{ color: 'white', textAlign: 'center' }}
        >
          <div className="flex justify-center items-center h-full">
            <FaUser />
          </div>
        </foreignObject>

        <text
          fill="white"
          x={30}
          y={-10}
          style={{ fontSize: '12px', fontWeight: 'bold' }}
        >
          {nodeDatum.name}
        </text>
        <text fill="white" x={30} y={5} style={{ fontSize: '10px' }}>
          {nodeDatum.email}
        </text>
        <text fill="#FFD700" x={30} y={20} style={{ fontSize: '10px' }}>
          Left PV: {nodeDatum.leftPV || 0}
        </text>
        <text fill="#FFD700" x={30} y={35} style={{ fontSize: '10px' }}>
          Right PV: {nodeDatum.rightPV || 0}
        </text>
      </g>
    ),
    []
  );

  // Transform the data to match react-d3-tree format
  const transformData = (data) => {
    if (!data || !data.name) {
      console.log('BinaryTree - Invalid data:', data);
      return null;
    }

    return {
      name: data.name || 'Unknown',
      email: data.email || 'No email',
      leftPV: data.leftPV || 0,
      rightPV: data.rightPV || 0,
      children: [
        data.leftLeg ? transformData(data.leftLeg) : null,
        data.rightLeg ? transformData(data.rightLeg) : null,
      ].filter(Boolean),
    };
  };

  console.log('BinaryTree - Input data:', data);
  const treeData = transformData(data);
  console.log('BinaryTree - Transformed data:', treeData);

  if (!treeData) {
    return (
      <div className="w-full h-[200px] border border-gray-300 rounded-lg bg-gray-100 flex items-center justify-center">
        <div className="text-gray-500 text-center">
          <p className="mb-2">No network data available</p>
          <p className="text-sm">Your binary network will appear here once you have downline members</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-[600px] border border-gray-300 rounded-lg bg-gray-100">
      <Tree
        data={treeData}
        orientation="vertical"
        renderCustomNodeElement={renderCustomNodeElement}
        pathFunc="step"
        separation={{ siblings: 2, nonSiblings: 2 }}
        translate={{ x: 300, y: 50 }}
        zoom={0.7}
        zoomable
        collapsible={false}
        nodeSize={{ x: 200, y: 100 }}
      />
    </div>
  );
};

export default BinaryTree;
