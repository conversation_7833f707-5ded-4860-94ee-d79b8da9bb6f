import { Link } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { FaExclamationTriangle, FaHome } from 'react-icons/fa';

const Unauthorized = () => {
  const { user } = useSelector((state) => state.auth);

  return (
    <div className="container mx-auto px-4 py-16 text-center">
      <div className="bg-white rounded-lg shadow-md p-8 max-w-md mx-auto">
        <div className="text-red-500 text-6xl mb-4">
          <FaExclamationTriangle className="inline-block" />
        </div>
        <h1 className="text-2xl font-bold text-gray-800 mb-4">Access Denied</h1>
        <p className="text-gray-600 mb-6">
          You do not have permission to access this page.
        </p>

        {/* Debug information */}
        <div className="bg-gray-100 p-4 rounded-md mb-6 text-left">
          <h2 className="font-bold mb-2">Debug Information:</h2>
          <p><strong>User:</strong> {user ? user.name : 'Not logged in'}</p>
          <p><strong>Email:</strong> {user ? user.email : 'N/A'}</p>
          <p><strong>Role (direct):</strong> {user ? user.role : 'N/A'}</p>
          <p><strong>Role (from data):</strong> {user && user.data ? user.data.role : 'N/A'}</p>
          <p><strong>isAdmin flag:</strong> {user && user.isAdmin ? 'true' : 'false'}</p>
          <p><strong>User ID:</strong> {user ? user._id : 'N/A'}</p>
          <p><strong>Token exists:</strong> {user && user.token ? 'Yes' : 'No'}</p>

          <div className="mt-4">
            <h3 className="font-bold">Full User Object:</h3>
            <pre className="bg-gray-200 p-2 rounded-md text-xs overflow-auto max-h-40">
              {user ? JSON.stringify(user, null, 2) : 'No user data'}
            </pre>
          </div>
        </div>

        <div className="flex justify-center space-x-4">
          <Link
            to="/dashboard"
            className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center"
          >
            <FaHome className="mr-2" /> Go to Dashboard
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Unauthorized;
