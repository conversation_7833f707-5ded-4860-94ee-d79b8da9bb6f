{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.0.1", "axios": "^1.6.2", "formik": "^2.4.5", "react": "^18.2.0", "react-d3-tree": "^3.6.1", "react-datepicker": "^8.3.0", "react-dom": "^18.2.0", "react-icons": "^4.12.0", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "react-toastify": "^9.1.3", "yup": "^1.3.2"}, "devDependencies": {"@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^5.0.5"}}