const mongoose = require('mongoose');

const commissionAuditSchema = new mongoose.Schema({
  // The user whose commission was adjusted
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  // The admin who made the adjustment
  admin: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  // The commission record that was adjusted (if applicable)
  commission: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Commission'
  },
  // Type of adjustment
  actionType: {
    type: String,
    enum: ['create', 'update', 'delete'],
    required: true
  },
  // Previous values (for updates)
  previousValues: {
    amount: Number,
    status: String,
    type: String,
    description: String
  },
  // New values
  newValues: {
    amount: Number,
    status: String,
    type: String,
    description: String
  },
  // Reason for adjustment
  reason: {
    type: String,
    required: true
  },
  // For direct wallet adjustments
  walletAdjustment: {
    amount: Number,
    previousBalance: Number,
    newBalance: Number
  },
  // Timestamp
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Add index for faster queries
commissionAuditSchema.index({ user: 1, createdAt: -1 });
commissionAuditSchema.index({ admin: 1, createdAt: -1 });

module.exports = mongoose.model('CommissionAudit', commissionAuditSchema);
