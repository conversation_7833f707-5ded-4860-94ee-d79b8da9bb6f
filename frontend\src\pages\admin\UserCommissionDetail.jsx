import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, Link, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { FaArrowLeft, FaRupeeSign, FaWallet, FaCalendarAlt, FaFilter, FaPlus } from 'react-icons/fa';
import Spinner from '../../components/Spinner';
import { getUserCommissionsById, adjustWallet, reset } from '../../features/mlm/mlmSlice';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

const UserCommissionDetail = () => {
  const { userId } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const { userCommissions, userWallet, isLoading, isSuccess, isError, message } = useSelector(
    (state) => state.mlm
  );

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCommissions, setTotalCommissions] = useState(0);

  // Filter state
  const [filters, setFilters] = useState({
    type: '',
    status: '',
    startDate: null,
    endDate: null,
  });

  // Wallet adjustment state
  const [showAdjustmentForm, setShowAdjustmentForm] = useState(false);
  const [adjustment, setAdjustment] = useState({
    amount: '',
    description: '',
    reason: '',
  });

  // Show filter panel state
  const [showFilters, setShowFilters] = useState(false);

  // Load user commissions on component mount
  useEffect(() => {
    if (userId) {
      loadUserCommissions();
    }
  }, [userId, currentPage]);

  // Handle success/error messages
  useEffect(() => {
    if (isError) {
      toast.error(message);
    }

    if (isSuccess && message) {
      toast.success(message);
      if (message === 'Wallet adjusted successfully') {
        setShowAdjustmentForm(false);
        setAdjustment({
          amount: '',
          description: '',
          reason: '',
        });
      }
    }

    dispatch(reset());
  }, [isError, isSuccess, message, dispatch]);

  // Load user commissions with current filters and pagination
  const loadUserCommissions = () => {
    const params = {
      page: currentPage,
      limit: 10,
      ...filters,
      startDate: filters.startDate ? filters.startDate.toISOString() : undefined,
      endDate: filters.endDate ? filters.endDate.toISOString() : undefined,
    };

    dispatch(getUserCommissionsById({ userId, params }))
      .unwrap()
      .then((data) => {
        setTotalPages(data.pages);
        setTotalCommissions(data.total);
      })
      .catch((error) => {
        console.error('Error loading user commissions:', error);
      });
  };

  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters((prev) => ({ ...prev, [name]: value }));
  };

  // Handle date filter changes
  const handleDateChange = (date, field) => {
    setFilters((prev) => ({ ...prev, [field]: date }));
  };

  // Apply filters
  const applyFilters = (e) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page when applying filters
    loadUserCommissions();
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      type: '',
      status: '',
      startDate: null,
      endDate: null,
    });
    setCurrentPage(1);
    loadUserCommissions();
  };

  // Handle wallet adjustment form changes
  const handleAdjustmentChange = (e) => {
    const { name, value } = e.target;
    setAdjustment((prev) => ({ ...prev, [name]: value }));
  };

  // Submit wallet adjustment
  const submitWalletAdjustment = (e) => {
    e.preventDefault();

    // Validate form
    if (!adjustment.amount || !adjustment.description || !adjustment.reason) {
      toast.error('Please fill in all fields');
      return;
    }

    // Convert amount to number
    const amount = parseFloat(adjustment.amount);
    if (isNaN(amount)) {
      toast.error('Amount must be a valid number');
      return;
    }

    // Confirm adjustment
    if (window.confirm(`Are you sure you want to ${amount >= 0 ? 'add' : 'deduct'} ₹${Math.abs(amount)} ${amount >= 0 ? 'to' : 'from'} this user's wallet?`)) {
      dispatch(
        adjustWallet({
          userId,
          amount,
          description: adjustment.description,
          reason: adjustment.reason,
        })
      );
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    });
  };

  // Format currency for display
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 2,
    }).format(amount);
  };

  // Handle pagination
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  if (isLoading) {
    return <Spinner />;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <button
            onClick={() => navigate('/admin/commissions')}
            className="mr-4 text-blue-600 hover:text-blue-800"
          >
            <FaArrowLeft className="inline mr-1" /> Back to All Commissions
          </button>
          <h1 className="text-2xl font-bold">User Commission Details</h1>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => setShowAdjustmentForm(!showAdjustmentForm)}
            className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center"
          >
            <FaWallet className="mr-2" /> Adjust Wallet
          </button>
          <Link
            to={`/admin/commissions/new?userId=${userId}`}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center"
          >
            <FaPlus className="mr-2" /> Create Commission
          </Link>
        </div>
      </div>

      {/* User Wallet Summary */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-lg font-semibold mb-4">Wallet Summary</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="text-sm text-blue-600 mb-1">Current Balance</div>
            <div className="text-2xl font-bold flex items-center">
              <FaRupeeSign className="mr-1 text-lg" />
              {userWallet ? formatCurrency(userWallet.balance) : '₹0.00'}
            </div>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="text-sm text-green-600 mb-1">Total Earned</div>
            <div className="text-2xl font-bold flex items-center">
              <FaRupeeSign className="mr-1 text-lg" />
              {userWallet && userWallet.transactions
                ? formatCurrency(
                    userWallet.transactions
                      .filter((t) => t.amount > 0)
                      .reduce((sum, t) => sum + t.amount, 0)
                  )
                : '₹0.00'}
            </div>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="text-sm text-purple-600 mb-1">Total Transactions</div>
            <div className="text-2xl font-bold">
              {userWallet && userWallet.transactions ? userWallet.transactions.length : 0}
            </div>
          </div>
        </div>
      </div>

      {/* Wallet Adjustment Form */}
      {showAdjustmentForm && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-lg font-semibold mb-4">Adjust Wallet Balance</h2>
          <form onSubmit={submitWalletAdjustment}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
                  Amount (₹)
                </label>
                <input
                  type="number"
                  id="amount"
                  name="amount"
                  value={adjustment.amount}
                  onChange={handleAdjustmentChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter amount (use negative for deductions)"
                  step="0.01"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  Use positive values to add funds, negative to deduct
                </p>
              </div>
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <input
                  type="text"
                  id="description"
                  name="description"
                  value={adjustment.description}
                  onChange={handleAdjustmentChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Description (visible to user)"
                  required
                />
              </div>
            </div>
            <div className="mb-4">
              <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-1">
                Reason for Adjustment
              </label>
              <textarea
                id="reason"
                name="reason"
                value={adjustment.reason}
                onChange={handleAdjustmentChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Internal reason for adjustment (for audit purposes)"
                rows="3"
                required
              ></textarea>
            </div>
            <div className="flex justify-end space-x-2">
              <button
                type="button"
                onClick={() => setShowAdjustmentForm(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Submit Adjustment
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Filter Panel */}
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">
            <FaFilter className="inline mr-2" /> Filters
          </h2>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="text-blue-600 hover:text-blue-800"
          >
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </button>
        </div>

        {showFilters && (
          <form onSubmit={applyFilters}>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
                  Commission Type
                </label>
                <select
                  id="type"
                  name="type"
                  value={filters.type}
                  onChange={handleFilterChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Types</option>
                  <option value="direct">Direct</option>
                  <option value="binary">Binary</option>
                  <option value="matching">Matching</option>
                  <option value="leadership">Leadership</option>
                  <option value="manual">Manual</option>
                </select>
              </div>

              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  id="status"
                  name="status"
                  value={filters.status}
                  onChange={handleFilterChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Statuses</option>
                  <option value="pending">Pending</option>
                  <option value="paid">Paid</option>
                  <option value="rejected">Rejected</option>
                </select>
              </div>

              <div className="md:col-span-1">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <FaCalendarAlt className="inline mr-1" /> Date Range
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <DatePicker
                    selected={filters.startDate}
                    onChange={(date) => handleDateChange(date, 'startDate')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholderText="Start date"
                    dateFormat="dd/MM/yyyy"
                    isClearable
                  />
                  <DatePicker
                    selected={filters.endDate}
                    onChange={(date) => handleDateChange(date, 'endDate')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholderText="End date"
                    dateFormat="dd/MM/yyyy"
                    isClearable
                    minDate={filters.startDate}
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <button
                type="button"
                onClick={resetFilters}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100"
              >
                Reset
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Apply Filters
              </button>
            </div>
          </form>
        )}
      </div>

      {/* Commission Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold">
            Commission History ({totalCommissions})
          </h2>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {userCommissions && userCommissions.length > 0 ? (
                userCommissions.map((commission) => (
                  <tr key={commission._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                        {commission.type}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <span className="font-medium">
                        <FaRupeeSign className="inline text-xs" /> {formatCurrency(commission.amount)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          commission.status === 'paid'
                            ? 'bg-green-100 text-green-800'
                            : commission.status === 'rejected'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}
                      >
                        {commission.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {commission.description}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(commission.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <Link
                        to={`/admin/commissions/edit/${commission._id}`}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        Edit
                      </Link>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="6" className="px-6 py-4 text-center text-gray-500">
                    No commission records found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 flex items-center justify-between border-t border-gray-200">
            <div>
              <p className="text-sm text-gray-700">
                Showing page <span className="font-medium">{currentPage}</span> of{' '}
                <span className="font-medium">{totalPages}</span>
              </p>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className={`px-3 py-1 rounded-md ${
                  currentPage === 1
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Previous
              </button>
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className={`px-3 py-1 rounded-md ${
                  currentPage === totalPages
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UserCommissionDetail;
