import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';
import { toast } from 'react-toastify';
import { FaArrowLeft, FaRupeeSign, FaSearch, FaExclamationTriangle } from 'react-icons/fa';
import Spinner from '../../components/Spinner';
import { createCommission, reset } from '../../features/mlm/mlmSlice';
import api from '../../utils/api';

const CommissionCreate = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  
  // Get userId from query params if available
  const queryParams = new URLSearchParams(location.search);
  const userIdFromQuery = queryParams.get('userId');
  
  const { isLoading, isSuccess, isError, message } = useSelector((state) => state.mlm);

  // Form state
  const [formData, setFormData] = useState({
    userId: userIdFromQuery || '',
    amount: '',
    type: 'manual',
    status: 'pending',
    description: '',
    reason: '',
  });

  // User search state
  const [userSearchQuery, setUserSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [selectedUser, setSelectedUser] = useState(null);
  const [searching, setSearching] = useState(false);

  // Load user data if userId is provided
  useEffect(() => {
    const fetchUser = async () => {
      if (formData.userId) {
        try {
          const response = await api.get(`/users/${formData.userId}`);
          setSelectedUser(response.data.data);
        } catch (error) {
          console.error('Error fetching user:', error);
        }
      }
    };

    fetchUser();
  }, [formData.userId]);

  // Handle success/error messages
  useEffect(() => {
    if (isError) {
      toast.error(message);
    }

    if (isSuccess) {
      toast.success('Commission created successfully');
      if (selectedUser) {
        navigate(`/admin/commissions/user/${selectedUser._id}`);
      } else {
        navigate('/admin/commissions');
      }
    }

    dispatch(reset());
  }, [isError, isSuccess, message, dispatch, navigate, selectedUser]);

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle amount change with validation
  const handleAmountChange = (e) => {
    const value = e.target.value;
    if (value === '' || (!isNaN(value) && parseFloat(value) >= 0)) {
      setFormData((prev) => ({ ...prev, amount: value }));
    }
  };

  // Search for users
  const searchUsers = async () => {
    if (!userSearchQuery.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      setSearching(true);
      const response = await api.get(`/users/search?query=${userSearchQuery}`);
      setSearchResults(response.data.data);
      setSearching(false);
    } catch (error) {
      console.error('Error searching users:', error);
      setSearching(false);
      toast.error('Failed to search users');
    }
  };

  // Handle user search input
  const handleSearchChange = (e) => {
    setUserSearchQuery(e.target.value);
  };

  // Handle user search submit
  const handleSearchSubmit = (e) => {
    e.preventDefault();
    searchUsers();
  };

  // Select a user from search results
  const selectUser = (user) => {
    setSelectedUser(user);
    setFormData((prev) => ({ ...prev, userId: user._id }));
    setSearchResults([]);
    setUserSearchQuery('');
  };

  // Submit form
  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate form
    if (!formData.userId || !formData.amount || !formData.type || !formData.status || !formData.description) {
      toast.error('Please fill in all required fields');
      return;
    }

    // Convert amount to number
    const amount = parseFloat(formData.amount);
    if (isNaN(amount)) {
      toast.error('Amount must be a valid number');
      return;
    }

    // Show confirmation if status is paid
    if (formData.status === 'paid') {
      if (!window.confirm(`This will add ₹${amount} to the user's wallet immediately. Are you sure?`)) {
        return;
      }
    }

    // Dispatch create action
    dispatch(createCommission(formData));
  };

  if (isLoading) {
    return <Spinner />;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <button
          onClick={() => navigate('/admin/commissions')}
          className="mr-4 text-blue-600 hover:text-blue-800"
        >
          <FaArrowLeft className="inline mr-1" /> Back to Commissions
        </button>
        <h1 className="text-2xl font-bold">Create New Commission</h1>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        {/* User Selection */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-4">Select User</h2>
          
          {selectedUser ? (
            <div className="bg-blue-50 p-4 rounded-md mb-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">{selectedUser.name}</p>
                  <p className="text-sm text-gray-600">{selectedUser.email}</p>
                  <p className="text-xs text-gray-500">ID: {selectedUser._id}</p>
                </div>
                <button
                  type="button"
                  onClick={() => {
                    setSelectedUser(null);
                    setFormData((prev) => ({ ...prev, userId: '' }));
                  }}
                  className="text-blue-600 hover:text-blue-800"
                >
                  Change User
                </button>
              </div>
            </div>
          ) : (
            <div>
              <form onSubmit={handleSearchSubmit} className="mb-4">
                <div className="flex">
                  <input
                    type="text"
                    value={userSearchQuery}
                    onChange={handleSearchChange}
                    className="flex-grow px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Search by name, email, or ID"
                  />
                  <button
                    type="submit"
                    className="bg-blue-600 text-white px-4 py-2 rounded-r-md hover:bg-blue-700"
                  >
                    <FaSearch />
                  </button>
                </div>
              </form>

              {searching && <p className="text-gray-600">Searching...</p>}

              {searchResults.length > 0 && (
                <div className="border border-gray-200 rounded-md overflow-hidden">
                  <ul className="divide-y divide-gray-200">
                    {searchResults.map((user) => (
                      <li
                        key={user._id}
                        className="p-3 hover:bg-gray-50 cursor-pointer"
                        onClick={() => selectUser(user)}
                      >
                        <p className="font-medium">{user.name}</p>
                        <p className="text-sm text-gray-600">{user.email}</p>
                        <p className="text-xs text-gray-500">ID: {user._id}</p>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Warning about wallet impact */}
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <FaExclamationTriangle className="h-5 w-5 text-yellow-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                <strong>Important:</strong> If you set the status to 'paid', the amount will be
                immediately added to the user's wallet balance.
              </p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
                Amount (₹) *
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaRupeeSign className="text-gray-400" />
                </div>
                <input
                  type="number"
                  id="amount"
                  name="amount"
                  value={formData.amount}
                  onChange={handleAmountChange}
                  className="w-full pl-8 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter amount"
                  step="0.01"
                  min="0"
                  required
                />
              </div>
            </div>

            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
                Commission Type *
              </label>
              <select
                id="type"
                name="type"
                value={formData.type}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="manual">Manual</option>
                <option value="direct">Direct</option>
                <option value="binary">Binary</option>
                <option value="matching">Matching</option>
                <option value="leadership">Leadership</option>
              </select>
            </div>

            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status *
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="pending">Pending</option>
                <option value="paid">Paid</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Description *
              </label>
              <input
                type="text"
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Description (visible to user)"
                required
              />
            </div>
          </div>

          <div className="mb-4">
            <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-1">
              Reason for Creation *
            </label>
            <textarea
              id="reason"
              name="reason"
              value={formData.reason}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Internal reason for creating this commission (for audit purposes)"
              rows="3"
              required
            ></textarea>
          </div>

          <div className="flex justify-end space-x-2">
            <button
              type="button"
              onClick={() => navigate('/admin/commissions')}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!selectedUser}
              className={`px-4 py-2 rounded-md ${
                !selectedUser
                  ? 'bg-gray-400 cursor-not-allowed text-white'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              Create Commission
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CommissionCreate;
