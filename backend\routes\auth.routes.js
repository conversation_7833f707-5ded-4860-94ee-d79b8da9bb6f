const express = require('express');
const { register, login, logout, getMe, updateProfile } = require('../controllers/auth.controller');
const { protect, verifyReferralCode } = require('../middleware/auth.middleware');

const router = express.Router();

router.post('/register', verifyReferralCode, register);
router.post('/login', login);
router.get('/logout', logout);
router.get('/me', protect, getMe);
router.put('/updateprofile', protect, updateProfile);

module.exports = router;
