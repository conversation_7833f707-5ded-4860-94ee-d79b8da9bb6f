import { Link } from 'react-router-dom';
import { FaFacebook, FaTwitter, FaInstagram, FaLinkedin } from 'react-icons/fa';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-800 text-white pt-10 pb-5">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-xl font-bold mb-4">Binary MLM E-commerce</h3>
            <p className="mb-4">
              Your trusted platform for quality products and business opportunities.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-white hover:text-blue-400">
                <FaFacebook size={20} />
              </a>
              <a href="#" className="text-white hover:text-blue-400">
                <FaTwitter size={20} />
              </a>
              <a href="#" className="text-white hover:text-blue-400">
                <FaInstagram size={20} />
              </a>
              <a href="#" className="text-white hover:text-blue-400">
                <FaLinkedin size={20} />
              </a>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="hover:text-gray-300">
                  Home
                </Link>
              </li>
              <li>
                <Link to="/products" className="hover:text-gray-300">
                  Products
                </Link>
              </li>
              <li>
                <Link to="/about" className="hover:text-gray-300">
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/contact" className="hover:text-gray-300">
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Business</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/business-opportunity" className="hover:text-gray-300">
                  Business Opportunity
                </Link>
              </li>
              <li>
                <Link to="/compensation-plan" className="hover:text-gray-300">
                  Compensation Plan
                </Link>
              </li>
              <li>
                <Link to="/success-stories" className="hover:text-gray-300">
                  Success Stories
                </Link>
              </li>
              <li>
                <Link to="/faq" className="hover:text-gray-300">
                  FAQ
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Contact Us</h3>
            <address className="not-italic">
              <p className="mb-2">123 Business Avenue</p>
              <p className="mb-2">New York, NY 10001</p>
              <p className="mb-2">Email: <EMAIL></p>
              <p>Phone: +****************</p>
            </address>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-8 pt-6 text-center">
          <p>
            &copy; {currentYear} Binary MLM E-commerce. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
