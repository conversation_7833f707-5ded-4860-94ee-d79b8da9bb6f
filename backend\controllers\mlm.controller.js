const User = require('../models/user.model');
const Commission = require('../models/commission.model');
const MlmConfig = require('../models/mlmConfig.model');
const { ErrorResponse } = require('../middleware/error.middleware');

// @desc    Calculate and distribute binary commissions
// @route   POST /api/mlm/calculate-commissions
// @access  Private/Admin
exports.calculateCommissions = async (req, res, next) => {
  try {
    // Get MLM configuration
    const mlmConfig = await MlmConfig.getConfig();

    // Get all users
    const users = await User.find({
      $and: [
        { leftPV: { $gte: mlmConfig.minPVThreshold } },
        { rightPV: { $gte: mlmConfig.minPVThreshold } }
      ]
    });

    const commissions = [];

    // Calculate commission for each eligible user
    for (const user of users) {
      // Determine weaker leg
      const weakerLegPV = Math.min(user.leftPV, user.rightPV);
      const strongerLegPV = Math.max(user.leftPV, user.rightPV);

      // Check leg ratio requirement if configured
      if (mlmConfig.legRatioRequirement) {
        const requiredRatio = mlmConfig.legRatioRequirement.stronger / mlmConfig.legRatioRequirement.weaker;
        const actualRatio = strongerLegPV / weakerLegPV;

        if (actualRatio < requiredRatio) {
          continue; // Skip this user if ratio requirement not met
        }
      }

      // Calculate commission amount
      let commissionAmount = (weakerLegPV * mlmConfig.binaryCommissionRate) / 100;

      // Apply maximum commission cap if configured
      if (mlmConfig.maxCommissionPerCycle && commissionAmount > mlmConfig.maxCommissionPerCycle) {
        commissionAmount = mlmConfig.maxCommissionPerCycle;
      }

      // Create commission record
      const commission = await Commission.create({
        user: user._id,
        amount: commissionAmount,
        type: 'binary',
        description: 'Binary commission',
        leftPV: user.leftPV,
        rightPV: user.rightPV,
        leftCarryForward: user.leftPV - weakerLegPV,
        rightCarryForward: user.rightPV - weakerLegPV,
        status: 'pending'
      });

      commissions.push(commission);

      // Update user's PV and wallet
      await User.findByIdAndUpdate(user._id, {
        leftPV: user.leftPV - weakerLegPV,
        rightPV: user.rightPV - weakerLegPV,
        $inc: { 'wallet.balance': commissionAmount },
        $push: {
          'wallet.transactions': {
            amount: commissionAmount,
            type: 'commission',
            description: 'Binary commission'
          }
        }
      });
    }

    res.status(200).json({
      success: true,
      count: commissions.length,
      data: commissions
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update MLM configuration
// @route   PUT /api/mlm/config
// @access  Private/Admin
exports.updateMlmConfig = async (req, res, next) => {
  try {
    const config = await MlmConfig.findOneAndUpdate({}, req.body, {
      new: true,
      runValidators: true,
      upsert: true
    });

    res.status(200).json({
      success: true,
      data: config
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get MLM configuration
// @route   GET /api/mlm/config
// @access  Private/Admin
exports.getMlmConfig = async (req, res, next) => {
  try {
    const config = await MlmConfig.getConfig();

    res.status(200).json({
      success: true,
      data: config
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Process PV from a purchase
// @route   POST /api/mlm/process-pv
// @access  Private
exports.processPV = async (req, res, next) => {
  try {
    const { userId, pointValue } = req.body;

    if (!userId || !pointValue) {
      return next(new ErrorResponse('User ID and Point Value are required', 400));
    }

    // Find the user
    const user = await User.findById(userId);

    if (!user) {
      return next(new ErrorResponse(`User not found with id of ${userId}`, 404));
    }

    // Get MLM configuration
    const mlmConfig = await MlmConfig.getConfig();

    // Update user's personal PV
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      { $inc: { personalPV: pointValue } },
      { new: true }
    );

    // Check if user has reached the minimum PV for activation
    if (!user.isActive && updatedUser.personalPV >= mlmConfig.minPVForActivation) {
      // Activate the user ID
      await User.findByIdAndUpdate(userId, { isActive: true });

      // Add a wallet transaction for activation
      await User.findByIdAndUpdate(userId, {
        $push: {
          'wallet.transactions': {
            amount: 0,
            type: 'commission',
            description: 'User ID activated after reaching minimum PV requirement'
          }
        }
      });
    }

    // Propagate PV up the binary tree
    await propagatePV(user.referrer, user.position, pointValue);

    res.status(200).json({
      success: true,
      message: 'PV processed successfully',
      data: {
        personalPV: updatedUser.personalPV,
        isActive: updatedUser.isActive || updatedUser.personalPV >= mlmConfig.minPVForActivation
      }
    });
  } catch (error) {
    next(error);
  }
};

// Helper function to propagate PV up the binary tree
const propagatePV = async (referrerId, position, pointValue) => {
  if (!referrerId) return;

  // Update referrer's PV based on position
  if (position === 'left') {
    await User.findByIdAndUpdate(referrerId, {
      $inc: { leftPV: pointValue }
    });
  } else {
    await User.findByIdAndUpdate(referrerId, {
      $inc: { rightPV: pointValue }
    });
  }

  // Get the updated referrer to continue up the tree
  const referrer = await User.findById(referrerId);

  if (referrer && referrer.referrer) {
    await propagatePV(referrer.referrer, referrer.position, pointValue);
  }
};

// @desc    Get PV to INR conversion rate
// @route   GET /api/mlm/pv-to-inr-rate
// @access  Public
exports.getPvToInrRate = async (req, res, next) => {
  try {
    const mlmConfig = await MlmConfig.getConfig();

    res.status(200).json({
      success: true,
      data: {
        pvToInrRate: mlmConfig.pvToInrRate,
        minPVForActivation: mlmConfig.minPVForActivation
      }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Convert PV to INR
// @route   GET /api/mlm/convert-pv/:pv
// @access  Public
exports.convertPvToInr = async (req, res, next) => {
  try {
    const pv = parseFloat(req.params.pv);

    if (isNaN(pv)) {
      return next(new ErrorResponse('Invalid PV value', 400));
    }

    const mlmConfig = await MlmConfig.getConfig();
    const inrValue = pv * mlmConfig.pvToInrRate;

    res.status(200).json({
      success: true,
      data: {
        pv,
        inrValue,
        rate: mlmConfig.pvToInrRate
      }
    });
  } catch (error) {
    next(error);
  }
};
