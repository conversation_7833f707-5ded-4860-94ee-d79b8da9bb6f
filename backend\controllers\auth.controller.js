const User = require('../models/user.model');
const { ErrorResponse } = require('../middleware/error.middleware');

// @desc    Register user
// @route   POST /api/auth/register
// @access  Public
exports.register = async (req, res, next) => {
  try {
    const { name, email, password, phone, position, referralCode } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return next(new ErrorResponse('Email already registered', 400));
    }

    // Get referrer from middleware
    const referrer = req.referrer;

    // Create new user
    const user = await User.create({
      name,
      email,
      password,
      phone,
      referrer: referrer._id,
      position
    });

    // Update referrer's binary structure
    if (position === 'left') {
      await User.findByIdAndUpdate(referrer._id, { leftLeg: user._id });
    } else {
      await User.findByIdAndUpdate(referrer._id, { rightLeg: user._id });
    }

    // Send token response
    sendTokenResponse(user, 201, res);
  } catch (error) {
    next(error);
  }
};

// @desc    Login user
// @route   POST /api/auth/login
// @access  Public
exports.login = async (req, res, next) => {
  try {
    const { email, password } = req.body;

    // Validate email & password
    if (!email || !password) {
      return next(new ErrorResponse('Please provide an email and password', 400));
    }

    // Check for user
    const user = await User.findOne({ email }).select('+password');

    if (!user) {
      return next(new ErrorResponse('Invalid credentials', 401));
    }

    // Check if password matches
    const isMatch = await user.comparePassword(password);

    if (!isMatch) {
      return next(new ErrorResponse('Invalid credentials', 401));
    }

    // Send token response
    sendTokenResponse(user, 200, res);
  } catch (error) {
    next(error);
  }
};

// @desc    Log user out / clear cookie
// @route   GET /api/auth/logout
// @access  Private
exports.logout = async (req, res, next) => {
  try {
    res.cookie('token', 'none', {
      expires: new Date(Date.now() + 10 * 1000),
      httpOnly: true
    });

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get current logged in user
// @route   GET /api/auth/me
// @access  Private
exports.getMe = async (req, res, next) => {
  try {
    console.log('getMe - User ID:', req.user._id);

    // Include PV values in the user profile
    const user = await User.findById(req.user._id)
      .select('+leftPV +rightPV +personalPV +wallet');

    if (!user) {
      console.log('getMe - User not found');
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    console.log('getMe - User found:', user.name);
    console.log('getMe - User role:', user.role);

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('getMe - Error:', error.message);
    next(error);
  }
};

// @desc    Update user profile
// @route   PUT /api/auth/updateprofile
// @access  Private
exports.updateProfile = async (req, res, next) => {
  try {
    console.log('updateProfile - User ID:', req.user._id);
    console.log('updateProfile - Request body:', req.body);

    // Fields to update
    const fieldsToUpdate = {
      name: req.body.name,
      email: req.body.email,
      phone: req.body.phone,
      address: req.body.address
    };

    // Remove undefined fields
    Object.keys(fieldsToUpdate).forEach(key =>
      fieldsToUpdate[key] === undefined && delete fieldsToUpdate[key]
    );

    console.log('updateProfile - Fields to update:', fieldsToUpdate);

    // Update user
    const user = await User.findByIdAndUpdate(req.user._id, fieldsToUpdate, {
      new: true,
      runValidators: true
    }).select('+leftPV +rightPV +personalPV +wallet');

    if (!user) {
      console.log('updateProfile - User not found');
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    console.log('updateProfile - User updated:', user.name);

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('updateProfile - Error:', error.message);
    next(error);
  }
};

// Helper function to send token response
const sendTokenResponse = (user, statusCode, res) => {
  // Create token
  const token = user.getSignedJwtToken();

  const options = {
    expires: new Date(
      Date.now() + process.env.COOKIE_EXPIRE * 24 * 60 * 60 * 1000
    ),
    httpOnly: true
  };

  // Add secure flag in production
  if (process.env.NODE_ENV === 'production') {
    options.secure = true;
  }

  // Include user role and ID in the response
  res
    .status(statusCode)
    .cookie('token', token, options)
    .json({
      success: true,
      token,
      role: user.role,
      _id: user._id,
      name: user.name,
      email: user.email,
      isAdmin: user.role === 'admin' // Add isAdmin flag for backward compatibility
    });
};
