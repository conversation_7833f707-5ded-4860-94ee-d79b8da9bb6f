const mongoose = require('mongoose');

const mlmConfigSchema = new mongoose.Schema({
  // Binary commission configuration
  binaryCommissionRate: {
    type: Number,
    required: true,
    default: 10, // 10% of the weaker leg PV
    min: 0,
    max: 100
  },
  // Minimum PV required in both legs to qualify for commission
  minPVThreshold: {
    type: Number,
    required: true,
    default: 100
  },
  // Maximum commission per day/week/cycle
  maxCommissionPerCycle: {
    type: Number,
    required: true,
    default: 1000
  },
  // Leg ratio requirement (e.g., 1:2, 1:3)
  legRatioRequirement: {
    stronger: {
      type: Number,
      default: 3
    },
    weaker: {
      type: Number,
      default: 1
    }
  },
  // Referral commission (direct referral bonus)
  referralCommissionRate: {
    type: Number,
    default: 5, // 5% of referred user's first purchase
    min: 0,
    max: 100
  },
  // Leadership bonus levels
  leadershipBonusLevels: [
    {
      level: Number,
      rate: Number
    }
  ],
  // Minimum withdrawal amount
  minWithdrawalAmount: {
    type: Number,
    default: 50
  },
  // Processing fee for withdrawals (percentage)
  withdrawalFee: {
    type: Number,
    default: 2, // 2% fee
    min: 0,
    max: 100
  },
  // PV to INR conversion rate
  pvToInrRate: {
    type: Number,
    required: true,
    default: 0.10, // 1 PV = 0.10 INR
    min: 0.01
  },
  // Minimum PV required for user ID activation
  minPVForActivation: {
    type: Number,
    required: true,
    default: 500, // User needs 500 PV to activate their ID
    min: 1
  },
  // Active status requirements
  activeStatusRequirements: {
    personalPV: {
      type: Number,
      default: 50 // Minimum personal PV to remain active
    },
    frequency: {
      type: String,
      enum: ['monthly', 'quarterly', 'yearly'],
      default: 'monthly'
    }
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Ensure only one configuration document exists
mlmConfigSchema.statics.getConfig = async function() {
  const config = await this.findOne();
  if (config) {
    return config;
  }

  // Create default config if none exists
  return await this.create({});
};

module.exports = mongoose.model('MlmConfig', mlmConfigSchema);
