const express = require('express');
const {
  createOrder,
  getOrderById,
  updateOrderToPaid,
  updateOrderToDelivered,
  getMyOrders,
  getOrders,
  updateOrderStatus,
  approveOrder,
  rejectOrder,
  approvePayment,
  rejectPayment,
  updateDeliveryTracking
} = require('../controllers/order.controller');
const { protect, authorize } = require('../middleware/auth.middleware');

const router = express.Router();

// My orders route - accessible by all authenticated users
router.get('/myorders', protect, getMyOrders);

// Protect all other routes
router.use(protect);

// User routes
router.route('/')
  .post(createOrder)
  .get(authorize('admin'), getOrders);

router.route('/:id')
  .get(getOrderById);

router.route('/:id/pay')
  .put(updateOrderToPaid);

// Admin routes
router.route('/:id/deliver')
  .put(authorize('admin'), updateOrderToDelivered);

router.route('/:id/status')
  .put(authorize('admin'), updateOrderStatus);

router.route('/:id/approve')
  .put(authorize('admin'), approveOrder);

router.route('/:id/reject')
  .put(authorize('admin'), rejectOrder);

// Payment approval/rejection routes
router.route('/:id/approve-payment')
  .put(authorize('admin'), approvePayment);

router.route('/:id/reject-payment')
  .put(authorize('admin'), rejectPayment);

// Delivery tracking route
router.route('/:id/tracking')
  .put(authorize('admin'), updateDeliveryTracking);

module.exports = router;
