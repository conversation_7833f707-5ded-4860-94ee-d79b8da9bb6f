// Test file for image utilities
import { getImageUrl, getProductImageUrl, getProductImageUrls } from './imageUtils';

// Mock environment variable
const originalEnv = import.meta.env;
beforeAll(() => {
  import.meta.env = { ...originalEnv, VITE_API_URL: 'http://localhost:5000/api' };
});

afterAll(() => {
  import.meta.env = originalEnv;
});

describe('Image Utilities', () => {
  describe('getImageUrl', () => {
    test('should return placeholder for null/undefined', () => {
      expect(getImageUrl(null)).toBe('https://via.placeholder.com/300');
      expect(getImageUrl(undefined)).toBe('https://via.placeholder.com/300');
      expect(getImageUrl('')).toBe('https://via.placeholder.com/300');
    });

    test('should return full URL for relative paths', () => {
      const relativePath = '/uploads/products/image.jpg';
      const expected = 'http://localhost:5000/uploads/products/image.jpg';
      expect(getImageUrl(relativePath)).toBe(expected);
    });

    test('should return as-is for full URLs', () => {
      const fullUrl = 'https://example.com/image.jpg';
      expect(getImageUrl(fullUrl)).toBe(fullUrl);
    });
  });

  describe('getProductImageUrl', () => {
    test('should return placeholder for products without images', () => {
      expect(getProductImageUrl(null)).toBe('https://via.placeholder.com/300');
      expect(getProductImageUrl({})).toBe('https://via.placeholder.com/300');
      expect(getProductImageUrl({ images: [] })).toBe('https://via.placeholder.com/300');
    });

    test('should return first image URL for products with images', () => {
      const product = {
        images: [
          { url: '/uploads/products/image1.jpg' },
          { url: '/uploads/products/image2.jpg' }
        ]
      };
      const expected = 'http://localhost:5000/uploads/products/image1.jpg';
      expect(getProductImageUrl(product)).toBe(expected);
    });
  });

  describe('getProductImageUrls', () => {
    test('should return placeholder array for products without images', () => {
      expect(getProductImageUrls(null)).toEqual(['https://via.placeholder.com/300']);
      expect(getProductImageUrls({})).toEqual(['https://via.placeholder.com/300']);
      expect(getProductImageUrls({ images: [] })).toEqual(['https://via.placeholder.com/300']);
    });

    test('should return all image URLs for products with images', () => {
      const product = {
        images: [
          { url: '/uploads/products/image1.jpg' },
          { url: '/uploads/products/image2.jpg' }
        ]
      };
      const expected = [
        'http://localhost:5000/uploads/products/image1.jpg',
        'http://localhost:5000/uploads/products/image2.jpg'
      ];
      expect(getProductImageUrls(product)).toEqual(expected);
    });
  });
});
