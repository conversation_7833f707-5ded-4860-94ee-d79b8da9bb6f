{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest --watchAll --verbose"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cloudinary": "^2.6.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "react-toastify": "^11.0.5"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^7.1.1"}}