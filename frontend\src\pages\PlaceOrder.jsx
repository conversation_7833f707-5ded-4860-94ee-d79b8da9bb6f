import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { FaMapMarkerAlt, FaCreditCard, FaShoppingBag } from 'react-icons/fa';
import { createOrder, reset } from '../features/orders/orderSlice';
import { clearCart } from '../features/cart/cartSlice';
import CheckoutSteps from '../components/CheckoutSteps';
import Spinner from '../components/Spinner';

const PlaceOrder = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const cart = useSelector((state) => state.cart);
  const { user } = useSelector((state) => state.auth);
  const { order, isLoading, isSuccess, isError, message } = useSelector((state) => state.orders);

  // Calculate prices
  const itemsPrice = cart.cartItems.reduce(
    (acc, item) => acc + item.price * item.qty,
    0
  );
  const shippingPrice = itemsPrice > 100 ? 0 : 10;
  const taxPrice = Number((0.15 * itemsPrice).toFixed(2));
  const totalPrice = (itemsPrice + shippingPrice + taxPrice).toFixed(2);

  // Calculate total PV
  const totalPV = cart.cartItems.reduce(
    (acc, item) => acc + item.pointValue * item.qty,
    0
  );

  useEffect(() => {
    if (!cart.shippingAddress.address) {
      navigate('/shipping');
    } else if (!cart.paymentMethod) {
      navigate('/payment');
    }

    if (isSuccess && order) {
      console.log('Order created successfully:', order);
      dispatch(clearCart());
      dispatch(reset());

      // Get the order ID from the response
      const orderId = order._id;

      if (cart.paymentMethod === 'razorpay') {
        navigate(`/order/${orderId}/pay`);
      } else {
        navigate(`/order/${orderId}`);
      }
    }

    if (isError) {
      console.error('Error creating order:', message);
      toast.error(message || 'Error creating order. Please try again.');
    }
  }, [cart, navigate, isSuccess, isError, message, order, dispatch]);

  const placeOrderHandler = () => {
    // Format the shipping address to match the backend model
    const shippingAddress = {
      street: cart.shippingAddress.address,
      city: cart.shippingAddress.city,
      state: cart.shippingAddress.state,
      postalCode: cart.shippingAddress.postalCode,
      country: cart.shippingAddress.country
    };

    // Debug cart items
    console.log('Cart items before mapping:', cart.cartItems);

    // Create the order data
    const orderData = {
      orderItems: cart.cartItems.map(item => {
        // Ensure product ID is valid
        if (!item.product) {
          console.error('Missing product ID for item:', item);
        }

        return {
          name: item.name,
          quantity: item.qty,
          image: item.image || 'https://via.placeholder.com/100',
          price: item.price,
          pointValue: item.pointValue,
          product: item.product, // This should be the MongoDB ID
          variant: item.variant
        };
      }),
      shippingAddress,
      paymentMethod: cart.paymentMethod,
      totalPrice: Number(totalPrice)
    };

    console.log('Placing order with data:', orderData);
    dispatch(createOrder(orderData));
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <CheckoutSteps step1 step2 step3 step4 />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Order Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Shipping */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-lg font-semibold mb-4 flex items-center">
              <FaMapMarkerAlt className="text-blue-600 mr-2" /> Shipping
            </h2>
            <p className="text-gray-700">
              <strong>Address:</strong> {cart.shippingAddress.address},{' '}
              {cart.shippingAddress.city}, {cart.shippingAddress.state},{' '}
              {cart.shippingAddress.postalCode}, {cart.shippingAddress.country}
            </p>
            <p className="text-gray-700 mt-2">
              <strong>Phone:</strong> {cart.shippingAddress.phone}
            </p>
          </div>

          {/* Payment Method */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-lg font-semibold mb-4 flex items-center">
              <FaCreditCard className="text-blue-600 mr-2" /> Payment Method
            </h2>
            <p className="text-gray-700">
              <strong>Method:</strong>{' '}
              {cart.paymentMethod === 'razorpay'
                ? 'Razorpay (Credit/Debit Card, UPI, Netbanking)'
                : cart.paymentMethod === 'wallet'
                ? 'Wallet Balance'
                : 'Cash on Delivery'}
            </p>
          </div>

          {/* Order Items */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-lg font-semibold mb-4 flex items-center">
              <FaShoppingBag className="text-blue-600 mr-2" /> Order Items
            </h2>

            {cart.cartItems.length === 0 ? (
              <div className="text-center py-4">
                <p className="text-gray-500">Your cart is empty</p>
                <Link to="/products" className="text-blue-600 mt-2 inline-block">
                  Go Shopping
                </Link>
              </div>
            ) : (
              <div className="divide-y">
                {cart.cartItems.map((item, index) => (
                  <div key={index} className="py-4 flex items-center">
                    <div className="w-16 h-16 flex-shrink-0">
                      <img
                        src={item.image}
                        alt={item.name}
                        className="w-full h-full object-cover rounded-md"
                      />
                    </div>

                    <div className="ml-4 flex-grow">
                      <Link
                        to={`/product/${item.product}`}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        {item.name}
                      </Link>

                      {item.variant && (
                        <p className="text-sm text-gray-600">
                          {item.variant.name}: {item.variant.value}
                        </p>
                      )}

                      <p className="text-sm text-green-600">PV: {item.pointValue} × {item.qty} = {item.pointValue * item.qty}</p>
                    </div>

                    <div className="text-right">
                      <p className="font-semibold">
                        {item.qty} × ₹{item.price.toFixed(2)} = ₹{(item.qty * item.price).toFixed(2)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Order Summary */}
        <div>
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-lg font-semibold mb-4">Order Summary</h2>

            <div className="space-y-3 mb-6">
              <div className="flex justify-between">
                <span className="text-gray-600">Items</span>
                <span>₹{itemsPrice.toFixed(2)}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">Shipping</span>
                <span>₹{shippingPrice.toFixed(2)}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">Tax</span>
                <span>₹{taxPrice.toFixed(2)}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">Total PV</span>
                <span className="text-green-600">{totalPV}</span>
              </div>

              <div className="border-t pt-3 mt-3">
                <div className="flex justify-between font-semibold">
                  <span>Total</span>
                  <span>₹{totalPrice}</span>
                </div>
              </div>
            </div>

            <button
              type="button"
              className={`w-full ${
                cart.cartItems.length === 0
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700'
              } text-white py-3 px-4 rounded-md font-semibold`}
              disabled={cart.cartItems.length === 0 || isLoading}
              onClick={placeOrderHandler}
            >
              {isLoading ? <Spinner small /> : 'Place Order'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlaceOrder;
