import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { logout, reset } from '../features/auth/authSlice';
import logo from "../assets/logo.png"
import { FaUser, FaSignOutAlt, FaShoppingCart, FaChartLine, FaUsersCog, FaBoxOpen, FaCog, FaClipboardList, FaMoneyBillWave } from 'react-icons/fa';

const Header = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isAdminMenuOpen, setIsAdminMenuOpen] = useState(false);

  // Check if user is admin
  const isAdmin = () => {
    if (!user) return false;
    const userRole = user?.role || (user?.data && user.data.role);
    return userRole === 'admin' || user?.isAdmin === true;
  };

  const onLogout = () => {
    dispatch(logout());
    dispatch(reset());
    navigate('/');
  };

  return (
    <header className="bg-gray-800 text-white">
      <div className="container mx-auto px-4 py-3 flex justify-between items-center">
        <Link to="/" className="text-xl font-bold">
         <img src={logo} alt="" style={{width: '200px'}}/>
        </Link>

        <div className="hidden md:flex items-center space-x-4">
          <Link to="/products" className="hover:text-gray-300">
            Products
          </Link>

          {user ? (
            <>
              {/* Admin Menu */}
              {isAdmin() && (
                <div className="relative group mr-4">
                  <button
                    className="flex items-center hover:text-gray-300"
                    onClick={() => setIsAdminMenuOpen(!isAdminMenuOpen)}
                  >
                    <FaUsersCog className="mr-1" />
                    Admin
                  </button>
                  <div
                    className={`absolute right-0 mt-2 w-48 bg-white text-gray-800 rounded shadow-lg z-10 ${
                      isAdminMenuOpen ? 'block' : 'hidden'
                    }`}
                  >
                    <Link
                      to="/admin/products"
                      className="block px-4 py-2 hover:bg-gray-100"
                      onClick={() => setIsAdminMenuOpen(false)}
                    >
                      <FaBoxOpen className="inline mr-2" />
                      Products
                    </Link>
                    <Link
                      to="/admin/orders"
                      className="block px-4 py-2 hover:bg-gray-100"
                      onClick={() => setIsAdminMenuOpen(false)}
                    >
                      <FaClipboardList className="inline mr-2" />
                      Orders
                    </Link>
                    <Link
                      to="/admin/users"
                      className="block px-4 py-2 hover:bg-gray-100"
                      onClick={() => setIsAdminMenuOpen(false)}
                    >
                      <FaUser className="inline mr-2" />
                      Users
                    </Link>
                    <Link
                      to="/admin/mlm-config"
                      className="block px-4 py-2 hover:bg-gray-100"
                      onClick={() => setIsAdminMenuOpen(false)}
                    >
                      <FaCog className="inline mr-2" />
                      MLM Config
                    </Link>
                    <Link
                      to="/admin/commissions"
                      className="block px-4 py-2 hover:bg-gray-100"
                      onClick={() => setIsAdminMenuOpen(false)}
                    >
                      <FaMoneyBillWave className="inline mr-2" />
                      Commissions
                    </Link>
                  </div>
                </div>
              )}

              {/* User Menu */}
              <div className="relative group">
                <button
                  className="flex items-center hover:text-gray-300"
                  onClick={() => setIsMenuOpen(!isMenuOpen)}
                >
                  <FaUser className="mr-1" />
                  {user.name}
                </button>
                <div
                  className={`absolute right-0 mt-2 w-48 bg-white text-gray-800 rounded shadow-lg z-10 ${
                    isMenuOpen ? 'block' : 'hidden'
                  }`}
                >
                  <Link
                    to="/dashboard"
                    className="block px-4 py-2 hover:bg-gray-100"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <FaChartLine className="inline mr-2" />
                    Dashboard
                  </Link>
                  <Link
                    to="/profile"
                    className="block px-4 py-2 hover:bg-gray-100"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <FaUser className="inline mr-2" />
                    Profile
                  </Link>
                  <button
                    onClick={() => {
                      setIsMenuOpen(false);
                      onLogout();
                    }}
                    className="block w-full text-left px-4 py-2 hover:bg-gray-100"
                  >
                    <FaSignOutAlt className="inline mr-2" />
                    Logout
                  </button>
                </div>
              </div>
              <Link to="/cart" className="hover:text-gray-300">
                <FaShoppingCart />
              </Link>
            </>
          ) : (
            <>
              <Link to="/login" className="hover:text-gray-300">
                Login
              </Link>
              <Link
                to="/register"
                className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded"
              >
                Register
              </Link>
            </>
          )}
        </div>

        {/* Mobile menu button */}
        <button
          className="md:hidden text-white focus:outline-none"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
        >
          <svg
            className="h-6 w-6 fill-current"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            {isMenuOpen ? (
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M18.278 16.864a1 1 0 0 1-1.414 1.414l-4.829-4.828-4.828 4.828a1 1 0 0 1-1.414-1.414l4.828-4.829-4.828-4.828a1 1 0 0 1 1.414-1.414l4.829 4.828 4.828-4.828a1 1 0 1 1 1.414 1.414l-4.828 4.829 4.828 4.828z"
              />
            ) : (
              <path
                fillRule="evenodd"
                d="M4 5h16a1 1 0 0 1 0 2H4a1 1 0 1 1 0-2zm0 6h16a1 1 0 0 1 0 2H4a1 1 0 0 1 0-2zm0 6h16a1 1 0 0 1 0 2H4a1 1 0 0 1 0-2z"
              />
            )}
          </svg>
        </button>
      </div>

      {/* Mobile menu */}
      <div
        className={`md:hidden bg-gray-700 ${isMenuOpen ? 'block' : 'hidden'}`}
      >
        <Link
          to="/products"
          className="block px-4 py-2 text-white hover:bg-gray-600"
          onClick={() => setIsMenuOpen(false)}
        >
          Products
        </Link>

        {user ? (
          <>
            {/* Admin Menu Items for Mobile */}
            {isAdmin() && (
              <>
                <div className="px-4 py-2 text-white bg-gray-600 font-semibold">
                  Admin Menu
                </div>
                <Link
                  to="/admin/products"
                  className="block px-4 py-2 text-white hover:bg-gray-600 pl-6"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <FaBoxOpen className="inline mr-2" />
                  Products
                </Link>
                <Link
                  to="/admin/orders"
                  className="block px-4 py-2 text-white hover:bg-gray-600 pl-6"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <FaClipboardList className="inline mr-2" />
                  Orders
                </Link>
                <Link
                  to="/admin/users"
                  className="block px-4 py-2 text-white hover:bg-gray-600 pl-6"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <FaUser className="inline mr-2" />
                  Users
                </Link>
                <Link
                  to="/admin/mlm-config"
                  className="block px-4 py-2 text-white hover:bg-gray-600 pl-6"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <FaCog className="inline mr-2" />
                  MLM Config
                </Link>
                <Link
                  to="/admin/commissions"
                  className="block px-4 py-2 text-white hover:bg-gray-600 pl-6"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <FaMoneyBillWave className="inline mr-2" />
                  Commissions
                </Link>
              </>
            )}

            {/* User Menu Items for Mobile */}
            <div className="px-4 py-2 text-white bg-gray-600 font-semibold">
              User Menu
            </div>
            <Link
              to="/dashboard"
              className="block px-4 py-2 text-white hover:bg-gray-600"
              onClick={() => setIsMenuOpen(false)}
            >
              <FaChartLine className="inline mr-2" />
              Dashboard
            </Link>
            <Link
              to="/profile"
              className="block px-4 py-2 text-white hover:bg-gray-600"
              onClick={() => setIsMenuOpen(false)}
            >
              <FaUser className="inline mr-2" />
              Profile
            </Link>
            <Link
              to="/cart"
              className="block px-4 py-2 text-white hover:bg-gray-600"
              onClick={() => setIsMenuOpen(false)}
            >
              <FaShoppingCart className="inline mr-2" />
              Cart
            </Link>
            <button
              onClick={() => {
                setIsMenuOpen(false);
                onLogout();
              }}
              className="block w-full text-left px-4 py-2 text-white hover:bg-gray-600"
            >
              <FaSignOutAlt className="inline mr-2" />
              Logout
            </button>
          </>
        ) : (
          <>
            <Link
              to="/login"
              className="block px-4 py-2 text-white hover:bg-gray-600"
              onClick={() => setIsMenuOpen(false)}
            >
              Login
            </Link>
            <Link
              to="/register"
              className="block px-4 py-2 text-white hover:bg-gray-600"
              onClick={() => setIsMenuOpen(false)}
            >
              Register
            </Link>
          </>
        )}
      </div>
    </header>
  );
};

export default Header;
