import { Link } from 'react-router-dom';
import { FaUser, FaT<PERSON>ck, FaCreditCard, FaCheckCircle } from 'react-icons/fa';

const CheckoutSteps = ({ step1, step2, step3, step4 }) => {
  return (
    <div className="flex justify-center mb-8">
      <div className="w-full max-w-3xl">
        <div className="flex items-center justify-between">
          {/* Step 1: Sign In */}
          <div className="flex flex-col items-center">
            <div className={`w-10 h-10 flex items-center justify-center rounded-full ${
              step1 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-500'
            }`}>
              <FaUser />
            </div>
            <div className={`mt-2 text-sm ${
              step1 ? 'text-blue-600 font-medium' : 'text-gray-500'
            }`}>
              {step1 ? (
                <Link to="/login">Sign In</Link>
              ) : (
                'Sign In'
              )}
            </div>
          </div>
          
          {/* Connector */}
          <div className={`flex-1 h-1 mx-2 ${
            step2 ? 'bg-blue-600' : 'bg-gray-200'
          }`}></div>
          
          {/* Step 2: Shipping */}
          <div className="flex flex-col items-center">
            <div className={`w-10 h-10 flex items-center justify-center rounded-full ${
              step2 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-500'
            }`}>
              <FaTruck />
            </div>
            <div className={`mt-2 text-sm ${
              step2 ? 'text-blue-600 font-medium' : 'text-gray-500'
            }`}>
              {step2 ? (
                <Link to="/shipping">Shipping</Link>
              ) : (
                'Shipping'
              )}
            </div>
          </div>
          
          {/* Connector */}
          <div className={`flex-1 h-1 mx-2 ${
            step3 ? 'bg-blue-600' : 'bg-gray-200'
          }`}></div>
          
          {/* Step 3: Payment */}
          <div className="flex flex-col items-center">
            <div className={`w-10 h-10 flex items-center justify-center rounded-full ${
              step3 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-500'
            }`}>
              <FaCreditCard />
            </div>
            <div className={`mt-2 text-sm ${
              step3 ? 'text-blue-600 font-medium' : 'text-gray-500'
            }`}>
              {step3 ? (
                <Link to="/payment">Payment</Link>
              ) : (
                'Payment'
              )}
            </div>
          </div>
          
          {/* Connector */}
          <div className={`flex-1 h-1 mx-2 ${
            step4 ? 'bg-blue-600' : 'bg-gray-200'
          }`}></div>
          
          {/* Step 4: Place Order */}
          <div className="flex flex-col items-center">
            <div className={`w-10 h-10 flex items-center justify-center rounded-full ${
              step4 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-500'
            }`}>
              <FaCheckCircle />
            </div>
            <div className={`mt-2 text-sm ${
              step4 ? 'text-blue-600 font-medium' : 'text-gray-500'
            }`}>
              {step4 ? (
                <Link to="/placeorder">Place Order</Link>
              ) : (
                'Place Order'
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutSteps;
