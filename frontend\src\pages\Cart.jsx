import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { FaTrash, FaArrowLeft, FaShoppingBag } from 'react-icons/fa';
import { addToCart, removeFromCart } from '../features/cart/cartSlice';

const Cart = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { cartItems } = useSelector((state) => state.cart);
  const { user } = useSelector((state) => state.auth);

  const [subTotal, setSubTotal] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPV, setTotalPV] = useState(0);

  useEffect(() => {
    // Calculate totals
    setSubTotal(cartItems.reduce((acc, item) => acc + item.price * item.qty, 0));
    setTotalItems(cartItems.reduce((acc, item) => acc + item.qty, 0));
    setTotalPV(cartItems.reduce((acc, item) => acc + item.pointValue * item.qty, 0));
  }, [cartItems]);

  const handleQuantityChange = (item, qty) => {
    dispatch(
      addToCart({
        ...item,
        qty: Number(qty),
      })
    );
  };

  const handleRemoveItem = (productId, variant) => {
    dispatch(removeFromCart({ productId, variant }));
  };

  const handleCheckout = () => {
    if (!user) {
      navigate('/login?redirect=shipping');
    } else {
      navigate('/shipping');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Shopping Cart</h1>

      {cartItems.length === 0 ? (
        <div className="bg-white p-8 rounded-lg shadow-md text-center">
          <FaShoppingBag className="mx-auto text-gray-300 text-6xl mb-4" />
          <h2 className="text-xl font-semibold mb-4">Your cart is empty</h2>
          <p className="text-gray-600 mb-6">
            Looks like you haven't added any products to your cart yet.
          </p>
          <Link
            to="/products"
            className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded-md inline-flex items-center"
          >
            <FaArrowLeft className="mr-2" /> Continue Shopping
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-4 border-b">
                <h2 className="text-lg font-semibold">Cart Items ({totalItems})</h2>
              </div>

              <div className="divide-y">
                {cartItems.map((item) => (
                  <div key={`${item.product}-${item.variant?.name}-${item.variant?.value}`} className="p-4 flex flex-col sm:flex-row">
                    <div className="sm:w-24 sm:h-24 mb-4 sm:mb-0 flex-shrink-0">
                      <img
                        src={item.image || 'https://via.placeholder.com/100'}
                        alt={item.name}
                        className="w-full h-full object-cover rounded-md"
                      />
                    </div>

                    <div className="sm:ml-4 flex-grow">
                      <div className="flex flex-col sm:flex-row sm:justify-between">
                        <div>
                          <Link to={`/product/${item.product}`} className="text-lg font-semibold text-gray-800 hover:text-blue-600">
                            {item.name}
                          </Link>

                          {item.variant && (
                            <p className="text-sm text-gray-600 mt-1">
                              {item.variant.name}: {item.variant.value}
                            </p>
                          )}

                          <p className="text-sm text-green-600 mt-1">PV: {item.pointValue} × {item.qty} = {item.pointValue * item.qty}</p>
                        </div>

                        <div className="mt-2 sm:mt-0 text-right">
                          <p className="text-lg font-bold">₹{(item.price * item.qty).toFixed(2)}</p>
                          <p className="text-sm text-gray-600">₹{item.price.toFixed(2)} each</p>
                        </div>
                      </div>

                      <div className="flex justify-between items-center mt-4">
                        <div className="flex items-center">
                          <label htmlFor={`qty-${item.product}`} className="mr-2 text-sm text-gray-600">
                            Qty:
                          </label>
                          <select
                            id={`qty-${item.product}`}
                            value={item.qty}
                            onChange={(e) => handleQuantityChange(item, e.target.value)}
                            className="border border-gray-300 rounded-md px-2 py-1 text-sm"
                          >
                            {[...Array(Math.min(item.stock, 10)).keys()].map((x) => (
                              <option key={x + 1} value={x + 1}>
                                {x + 1}
                              </option>
                            ))}
                          </select>
                        </div>

                        <button
                          onClick={() => handleRemoveItem(item.product, item.variant)}
                          className="text-red-500 hover:text-red-700"
                          title="Remove Item"
                        >
                          <FaTrash />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div>
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold mb-4">Order Summary</h2>

              <div className="space-y-3 mb-6">
                <div className="flex justify-between text-gray-600">
                  <span>Subtotal ({totalItems} items)</span>
                  <span>₹{subTotal.toFixed(2)}</span>
                </div>

                <div className="flex justify-between text-gray-600">
                  <span>Shipping</span>
                  <span>Calculated at checkout</span>
                </div>

                <div className="flex justify-between text-gray-600">
                  <span>Total PV</span>
                  <span className="text-green-600">{totalPV}</span>
                </div>

                <div className="border-t pt-3 mt-3">
                  <div className="flex justify-between font-semibold">
                    <span>Estimated Total</span>
                    <span>₹{subTotal.toFixed(2)}</span>
                  </div>
                </div>
              </div>

              <button
                onClick={handleCheckout}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-md font-semibold"
              >
                Proceed to Checkout
              </button>

              <div className="mt-4">
                <Link
                  to="/products"
                  className="text-blue-600 hover:text-blue-800 flex items-center justify-center"
                >
                  <FaArrowLeft className="mr-2" /> Continue Shopping
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Cart;
