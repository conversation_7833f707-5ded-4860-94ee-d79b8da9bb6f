const express = require('express');
const {
  getUsers,
  getUser,
  createUser,
  updateUser,
  deleteUser,
  getUserDownline,
  searchUsers
} = require('../controllers/user.controller');
const { protect, authorize } = require('../middleware/auth.middleware');

const router = express.Router();

// User downline route - accessible by all authenticated users
// Important: The /downline route must come before other routes to avoid path conflicts
router.get('/downline', protect, getUserDownline); // Get current user's downline

// Search route - must come before the ID routes to avoid conflicts
router.get('/search', protect, authorize('admin'), searchUsers);

// Protect all other routes
router.use(protect);

// Admin only routes
router.route('/')
  .get(authorize('admin'), getUsers)
  .post(authorize('admin'), createUser);

router.route('/:id')
  .get(authorize('admin'), getUser)
  .put(authorize('admin'), updateUser)
  .delete(authorize('admin'), deleteUser);

router.get('/:id/downline', authorize('admin'), getUserDownline); // Admin can get any user's downline

module.exports = router;
