const express = require('express');
const {
  calculateCommissions,
  updateMlmConfig,
  getMlmConfig,
  processPV,
  getPvToInrRate,
  convertPvToInr
} = require('../controllers/mlm.controller');

const {
  getAllCommissions,
  getCommission,
  getUserCommissions,
  createCommission,
  updateCommission,
  getCommissionAuditLogs,
  adjustWallet
} = require('../controllers/commission.controller');

const { protect, authorize } = require('../middleware/auth.middleware');

const router = express.Router();

// Protect all routes
router.use(protect);

// Admin routes
router.route('/calculate-commissions')
  .post(authorize('admin'), calculateCommissions);

router.route('/config')
  .get(authorize('admin'), getMlmConfig)
  .put(authorize('admin'), updateMlmConfig);

// Commission management routes (admin only)
router.route('/commissions')
  .get(authorize('admin'), getAllCommissions)
  .post(authorize('admin'), createCommission);

// Important: Specific routes must come before parameterized routes
router.route('/commissions/user/:userId')
  .get(authorize('admin'), getUserCommissions);

router.route('/commissions/audit')
  .get(authorize('admin'), getCommissionAuditLogs);

router.route('/commissions/wallet-adjustment')
  .post(authorize('admin'), adjustWallet);

// This must come after the specific routes to avoid conflicts
router.route('/commissions/:id')
  .get(authorize('admin'), getCommission)
  .put(authorize('admin'), updateCommission);

// Process PV route - accessible by all authenticated users
router.route('/process-pv')
  .post(authorize('admin', 'user', 'franchise'), processPV);

// PV to INR conversion routes - public routes
router.route('/pv-to-inr-rate')
  .get(getPvToInrRate);

router.route('/convert-pv/:pv')
  .get(convertPvToInr);

module.exports = router;
