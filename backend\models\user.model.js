const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please enter your name'],
    trim: true,
    maxLength: [50, 'Name cannot exceed 50 characters']
  },
  email: {
    type: String,
    required: [true, 'Please enter your email'],
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  password: {
    type: String,
    required: [true, 'Please enter your password'],
    minLength: [6, 'Password must be at least 6 characters'],
    select: false
  },
  phone: {
    type: String,
    required: [true, 'Please enter your phone number'],
    trim: true
  },
  address: {
    street: String,
    city: String,
    state: String,
    postalCode: String,
    country: String
  },
  role: {
    type: String,
    enum: ['user', 'franchise', 'admin'],
    default: 'user'
  },
  // MLM Structure
  referrer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  referralCode: {
    type: String,
    unique: true
  },
  position: {
    type: String,
    enum: ['left', 'right'],
    required: function() {
      return this.referrer !== undefined;
    }
  },
  leftLeg: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  rightLeg: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  // Point Value (PV) tracking
  leftPV: {
    type: Number,
    default: 0,
    required: true
  },
  rightPV: {
    type: Number,
    default: 0,
    required: true
  },
  personalPV: {
    type: Number,
    default: 0,
    required: true
  },
  // Wallet
  wallet: {
    balance: {
      type: Number,
      default: 0
    },
    transactions: [
      {
        amount: Number,
        type: {
          type: String,
          enum: ['commission', 'withdrawal', 'refund']
        },
        description: String,
        timestamp: {
          type: Date,
          default: Date.now
        }
      }
    ]
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Encrypt password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) {
    next();
  }

  const salt = await bcrypt.genSalt(10);
  this.password = await bcrypt.hash(this.password, salt);

  // Generate referral code if not already set
  if (!this.referralCode) {
    this.referralCode = this._id.toString().slice(-8).toUpperCase();
  }
});

// Compare password method
userSchema.methods.comparePassword = async function(enteredPassword) {
  return await bcrypt.compare(enteredPassword, this.password);
};

// Generate JWT token
userSchema.methods.getSignedJwtToken = function() {
  return jwt.sign(
    { id: this._id, role: this.role },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRE }
  );
};

module.exports = mongoose.model('User', userSchema);
