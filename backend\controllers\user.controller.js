const User = require('../models/user.model');
const { ErrorResponse } = require('../middleware/error.middleware');

// @desc    Get all users
// @route   GET /api/users
// @access  Private/Admin
exports.getUsers = async (req, res, next) => {
  try {
    const users = await User.find();

    res.status(200).json({
      success: true,
      count: users.length,
      data: users
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single user
// @route   GET /api/users/:id
// @access  Private/Admin
exports.getUser = async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return next(new ErrorResponse(`User not found with id of ${req.params.id}`, 404));
    }

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create user
// @route   POST /api/users
// @access  Private/Admin
exports.createUser = async (req, res, next) => {
  try {
    const user = await User.create(req.body);

    res.status(201).json({
      success: true,
      data: user
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update user
// @route   PUT /api/users/:id
// @access  Private/Admin
exports.updateUser = async (req, res, next) => {
  try {
    const user = await User.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true
    });

    if (!user) {
      return next(new ErrorResponse(`User not found with id of ${req.params.id}`, 404));
    }

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete user
// @route   DELETE /api/users/:id
// @access  Private/Admin
exports.deleteUser = async (req, res, next) => {
  try {
    const user = await User.findByIdAndDelete(req.params.id);

    if (!user) {
      return next(new ErrorResponse(`User not found with id of ${req.params.id}`, 404));
    }

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Search users by name, email, or ID
// @route   GET /api/users/search
// @access  Private/Admin
exports.searchUsers = async (req, res, next) => {
  try {
    const { query } = req.query;

    if (!query) {
      return res.status(200).json({
        success: true,
        count: 0,
        data: []
      });
    }

    // Create a regex for case-insensitive search
    const searchRegex = new RegExp(query, 'i');

    // Search by name, email, or ID
    const users = await User.find({
      $or: [
        { name: searchRegex },
        { email: searchRegex },
        // If the query is a valid ObjectId, also search by ID
        ...(query.match(/^[0-9a-fA-F]{24}$/) ? [{ _id: query }] : [])
      ]
    }).select('_id name email referralCode').limit(10);

    res.status(200).json({
      success: true,
      count: users.length,
      data: users
    });
  } catch (error) {
    console.error('searchUsers - Error:', error);
    next(error);
  }
};

// @desc    Get user's downline (binary tree)
// @route   GET /api/users/:id/downline or GET /api/users/downline
// @access  Private
exports.getUserDownline = async (req, res, next) => {
  try {
    console.log('getUserDownline - Request params:', req.params);
    console.log('getUserDownline - User:', req.user ? req.user._id : 'None');

    // If id param is provided and user is admin, use that id, otherwise use the authenticated user's id
    const userId = req.params.id || req.user._id;
    console.log('getUserDownline - Using userId:', userId);

    const user = await User.findById(userId)
      .populate({
        path: 'leftLeg',
        select: 'name email referralCode leftPV rightPV leftLeg rightLeg'
      })
      .populate({
        path: 'rightLeg',
        select: 'name email referralCode leftPV rightPV leftLeg rightLeg'
      });

    if (!user) {
      console.log('getUserDownline - User not found');
      return next(new ErrorResponse(`User not found with id of ${userId}`, 404));
    }

    console.log('getUserDownline - User found:', user.name);

    // Recursive function to build the downline tree
    const buildDownlineTree = async (userId) => {
      if (!userId) return null;

      const member = await User.findById(userId)
        .select('name email referralCode leftPV rightPV leftLeg rightLeg');

      if (!member) return null;

      return {
        _id: member._id,
        name: member.name,
        email: member.email,
        referralCode: member.referralCode,
        leftPV: member.leftPV,
        rightPV: member.rightPV,
        leftLeg: member.leftLeg ? await buildDownlineTree(member.leftLeg) : null,
        rightLeg: member.rightLeg ? await buildDownlineTree(member.rightLeg) : null
      };
    };

    const downlineTree = {
      _id: user._id,
      name: user.name,
      email: user.email,
      referralCode: user.referralCode,
      leftPV: user.leftPV || 0,
      rightPV: user.rightPV || 0,
      leftLeg: user.leftLeg ? await buildDownlineTree(user.leftLeg) : null,
      rightLeg: user.rightLeg ? await buildDownlineTree(user.rightLeg) : null
    };

    console.log('getUserDownline - Downline tree built successfully');

    res.status(200).json({
      success: true,
      data: downlineTree
    });
  } catch (error) {
    console.error('getUserDownline - Error:', error.message);
    next(error);
  }
};
