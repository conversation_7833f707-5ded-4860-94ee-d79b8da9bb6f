const Order = require('../models/order.model');
const Product = require('../models/product.model');
const { ErrorResponse } = require('../middleware/error.middleware');
const mlmController = require('./mlm.controller');

// @desc    Create new order
// @route   POST /api/orders
// @access  Private
exports.createOrder = async (req, res, next) => {
  try {
    const {
      orderItems,
      shippingAddress,
      paymentMethod,
      totalPrice
    } = req.body;

    if (!orderItems || orderItems.length === 0) {
      return next(new ErrorResponse('No order items', 400));
    }

    // Validate all order items have valid product IDs
    for (const item of orderItems) {
      if (!item.product) {
        return next(new ErrorResponse('One or more items are missing product ID', 400));
      }
    }

    // Calculate total point value
    let totalPointValue = 0;

    // Verify products and update stock
    for (const item of orderItems) {
      try {
        // Validate product ID
        if (!item.product) {
          return next(new ErrorResponse('Product ID is missing for an item', 400));
        }

        console.log('Verifying product with ID:', item.product);
        const product = await Product.findById(item.product);

        if (!product) {
          return next(new ErrorResponse(`Product not found with id of ${item.product}`, 404));
        }

        // Check if product is in stock
        if (product.stock < item.quantity) {
          return next(new ErrorResponse(`Product ${product.name} is out of stock`, 400));
        }

        // Update stock
        product.stock -= item.quantity;
        await product.save();

        // Add point value to total
        totalPointValue += product.pointValue * item.quantity;

        // Add point value to order item
        item.pointValue = product.pointValue;
      } catch (error) {
        console.error('Error processing order item:', error);
        return next(new ErrorResponse(`Error processing product: ${error.message}`, 400));
      }
    }

    // Log request information for debugging
    console.log('createOrder - User:', req.user);
    console.log('createOrder - Order data:', {
      orderItems,
      shippingAddress,
      paymentMethod,
      totalPrice,
      totalPointValue
    });

    // Create order with user ID from req.user._id
    const order = await Order.create({
      user: req.user._id, // Use _id instead of id
      orderItems,
      shippingAddress,
      paymentMethod,
      totalPrice,
      totalPointValue
    });

    res.status(201).json({
      success: true,
      data: order
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get order by ID
// @route   GET /api/orders/:id
// @access  Private
exports.getOrderById = async (req, res, next) => {
  try {
    const order = await Order.findById(req.params.id).populate('user', 'name email');

    if (!order) {
      return next(new ErrorResponse(`Order not found with id of ${req.params.id}`, 404));
    }

    // Check if order belongs to user or user is admin
    console.log('getOrderById - Order user:', order.user._id);
    console.log('getOrderById - Request user:', req.user._id);

    if (order.user._id.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return next(new ErrorResponse('Not authorized to access this order', 401));
    }

    res.status(200).json({
      success: true,
      data: order
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update order to paid
// @route   PUT /api/orders/:id/pay
// @access  Private
exports.updateOrderToPaid = async (req, res, next) => {
  try {
    const order = await Order.findById(req.params.id);

    if (!order) {
      return next(new ErrorResponse(`Order not found with id of ${req.params.id}`, 404));
    }

    // Update order
    order.isPaid = true;
    order.paidAt = Date.now();
    order.paymentResult = {
      id: req.body.id,
      status: req.body.status,
      update_time: req.body.update_time,
      email_address: req.body.email_address
    };

    const updatedOrder = await order.save();

    // Process PV for MLM
    if (!order.commissionProcessed) {
      // Process PV
      await mlmController.processPV({
        body: {
          userId: order.user,
          pointValue: order.totalPointValue
        }
      }, res, next);

      // Mark commission as processed
      order.commissionProcessed = true;
      await order.save();
    }

    res.status(200).json({
      success: true,
      data: updatedOrder
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update order to delivered
// @route   PUT /api/orders/:id/deliver
// @access  Private/Admin
exports.updateOrderToDelivered = async (req, res, next) => {
  try {
    const order = await Order.findById(req.params.id);

    if (!order) {
      return next(new ErrorResponse(`Order not found with id of ${req.params.id}`, 404));
    }

    // Update order
    order.isDelivered = true;
    order.deliveredAt = Date.now();
    order.status = 'delivered';

    const updatedOrder = await order.save();

    res.status(200).json({
      success: true,
      data: updatedOrder
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get logged in user orders
// @route   GET /api/orders/myorders
// @access  Private
exports.getMyOrders = async (req, res, next) => {
  try {
    console.log('getMyOrders - User ID:', req.user._id);

    const orders = await Order.find({ user: req.user._id });

    console.log('getMyOrders - Orders found:', orders.length);

    res.status(200).json({
      success: true,
      count: orders.length,
      data: orders
    });
  } catch (error) {
    console.error('getMyOrders - Error:', error.message);
    next(error);
  }
};

// @desc    Get all orders
// @route   GET /api/orders
// @access  Private/Admin
exports.getOrders = async (req, res, next) => {
  try {
    // Add pagination and sorting
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    // Sort by createdAt in descending order (newest first)
    const sort = { createdAt: -1 };

    // Create query
    const query = {};

    // Filter by status if provided
    if (req.query.status) {
      query.status = req.query.status;
    }

    // Filter by approvalStatus if provided
    if (req.query.approvalStatus) {
      query.approvalStatus = req.query.approvalStatus;
    }

    // Count total documents
    const total = await Order.countDocuments(query);

    // Find orders with pagination and populate user
    const orders = await Order.find(query)
      .populate('user', 'id name email')
      .sort(sort)
      .skip(startIndex)
      .limit(limit);

    // Calculate total pages
    const pages = Math.ceil(total / limit);

    res.status(200).json({
      success: true,
      count: orders.length,
      total,
      pages,
      page,
      data: orders
    });
  } catch (error) {
    console.error('getOrders - Error:', error.message);
    next(error);
  }
};

// @desc    Approve order
// @route   PUT /api/orders/:id/approve
// @access  Private/Admin
exports.approveOrder = async (req, res, next) => {
  try {
    console.log('approveOrder - Order ID:', req.params.id);

    const order = await Order.findById(req.params.id);

    if (!order) {
      return next(new ErrorResponse(`Order not found with id of ${req.params.id}`, 404));
    }

    // Update order approval status
    order.approvalStatus = 'approved';
    order.approvedAt = Date.now();

    // Also update the main status if it's still pending
    if (order.status === 'pending') {
      order.status = 'processing';
    }

    const updatedOrder = await order.save();

    res.status(200).json({
      success: true,
      data: updatedOrder
    });
  } catch (error) {
    console.error('approveOrder - Error:', error.message);
    next(error);
  }
};

// @desc    Reject order
// @route   PUT /api/orders/:id/reject
// @access  Private/Admin
exports.rejectOrder = async (req, res, next) => {
  try {
    console.log('rejectOrder - Order ID:', req.params.id);

    const { reason } = req.body;

    const order = await Order.findById(req.params.id);

    if (!order) {
      return next(new ErrorResponse(`Order not found with id of ${req.params.id}`, 404));
    }

    // Update order approval status
    order.approvalStatus = 'rejected';
    order.rejectedAt = Date.now();
    order.rejectionReason = reason || 'No reason provided';

    // Also update the main status
    order.status = 'cancelled';

    const updatedOrder = await order.save();

    res.status(200).json({
      success: true,
      data: updatedOrder
    });
  } catch (error) {
    console.error('rejectOrder - Error:', error.message);
    next(error);
  }
};

// @desc    Update order status
// @route   PUT /api/orders/:id/status
// @access  Private/Admin
exports.updateOrderStatus = async (req, res, next) => {
  try {
    const { status, note } = req.body;

    if (!status) {
      return next(new ErrorResponse('Please provide a status', 400));
    }

    const order = await Order.findById(req.params.id);

    if (!order) {
      return next(new ErrorResponse(`Order not found with id of ${req.params.id}`, 404));
    }

    // Check if payment is approved for processing, shipping, or delivery
    if (['processing', 'shipped', 'delivered'].includes(status) &&
        order.paymentStatus !== 'approved' &&
        order.paymentMethod !== 'cod') {
      return next(new ErrorResponse('Payment must be approved before processing this order', 400));
    }

    // Store previous status for logging
    const previousStatus = order.status;

    // Update order status
    order.status = status;

    // Add to status logs
    order.statusLogs.push({
      status,
      timestamp: Date.now(),
      user: req.user._id,
      note: note || `Status changed from ${previousStatus} to ${status}`
    });

    // If status is delivered, update delivered fields
    if (status === 'delivered') {
      order.isDelivered = true;
      order.deliveredAt = Date.now();
    }

    const updatedOrder = await order.save();

    res.status(200).json({
      success: true,
      data: updatedOrder
    });
  } catch (error) {
    console.error('updateOrderStatus - Error:', error.message);
    next(error);
  }
};

// @desc    Update delivery tracking information
// @route   PUT /api/orders/:id/tracking
// @access  Private/Admin
exports.updateDeliveryTracking = async (req, res, next) => {
  try {
    const { trackingNumber, carrier, estimatedDeliveryDate, trackingUrl } = req.body;

    if (!trackingNumber || !carrier) {
      return next(new ErrorResponse('Please provide tracking number and carrier', 400));
    }

    const order = await Order.findById(req.params.id);

    if (!order) {
      return next(new ErrorResponse(`Order not found with id of ${req.params.id}`, 404));
    }

    // Update delivery tracking information
    order.deliveryTracking = {
      trackingNumber,
      carrier,
      estimatedDeliveryDate: estimatedDeliveryDate ? new Date(estimatedDeliveryDate) : undefined,
      trackingUrl,
      lastUpdated: Date.now()
    };

    // If order is in processing status, update to shipped
    if (order.status === 'processing') {
      order.status = 'shipped';

      // Add to status logs
      order.statusLogs.push({
        status: 'shipped',
        timestamp: Date.now(),
        user: req.user._id,
        note: `Order shipped with ${carrier}, tracking #${trackingNumber}`
      });
    } else {
      // Add to status logs
      order.statusLogs.push({
        status: 'tracking_updated',
        timestamp: Date.now(),
        user: req.user._id,
        note: `Tracking information updated: ${carrier}, #${trackingNumber}`
      });
    }

    const updatedOrder = await order.save();

    res.status(200).json({
      success: true,
      data: updatedOrder
    });
  } catch (error) {
    console.error('updateDeliveryTracking - Error:', error.message);
    next(error);
  }
};

// @desc    Approve payment for order
// @route   PUT /api/orders/:id/approve-payment
// @access  Private/Admin
exports.approvePayment = async (req, res, next) => {
  try {
    console.log('approvePayment - Order ID:', req.params.id);

    const order = await Order.findById(req.params.id);

    if (!order) {
      return next(new ErrorResponse(`Order not found with id of ${req.params.id}`, 404));
    }

    // Update payment status
    order.paymentStatus = 'approved';
    order.paymentApprovedAt = Date.now();
    order.isPaid = true;
    order.paidAt = Date.now();

    // Add to status logs
    order.statusLogs.push({
      status: 'payment_approved',
      timestamp: Date.now(),
      user: req.user._id,
      note: req.body.note || 'Payment approved by admin'
    });

    // If order is in pending status, move it to processing
    if (order.status === 'pending') {
      order.status = 'processing';

      // Add to status logs
      order.statusLogs.push({
        status: 'processing',
        timestamp: Date.now(),
        user: req.user._id,
        note: 'Order moved to processing after payment approval'
      });
    }

    const updatedOrder = await order.save();

    // Process PV for MLM if not already processed
    if (!order.commissionProcessed) {
      try {
        // Process PV
        await mlmController.processPV({
          body: {
            userId: order.user,
            pointValue: order.totalPointValue
          }
        }, res, next);

        // Mark commission as processed
        order.commissionProcessed = true;
        await order.save();
      } catch (error) {
        console.error('Error processing commission:', error);
        // Continue with the response even if commission processing fails
      }
    }

    res.status(200).json({
      success: true,
      data: updatedOrder
    });
  } catch (error) {
    console.error('approvePayment - Error:', error.message);
    next(error);
  }
};

// @desc    Reject payment for order
// @route   PUT /api/orders/:id/reject-payment
// @access  Private/Admin
exports.rejectPayment = async (req, res, next) => {
  try {
    console.log('rejectPayment - Order ID:', req.params.id);

    const { reason } = req.body;

    if (!reason) {
      return next(new ErrorResponse('Please provide a reason for payment rejection', 400));
    }

    const order = await Order.findById(req.params.id);

    if (!order) {
      return next(new ErrorResponse(`Order not found with id of ${req.params.id}`, 404));
    }

    // Update payment status
    order.paymentStatus = 'rejected';
    order.paymentRejectedAt = Date.now();
    order.paymentRejectionReason = reason;

    // Add to status logs
    order.statusLogs.push({
      status: 'payment_rejected',
      timestamp: Date.now(),
      user: req.user._id,
      note: `Payment rejected: ${reason}`
    });

    // Update order status to cancelled
    order.status = 'cancelled';

    // Add to status logs
    order.statusLogs.push({
      status: 'cancelled',
      timestamp: Date.now(),
      user: req.user._id,
      note: 'Order cancelled due to payment rejection'
    });

    const updatedOrder = await order.save();

    res.status(200).json({
      success: true,
      data: updatedOrder
    });
  } catch (error) {
    console.error('rejectPayment - Error:', error.message);
    next(error);
  }
};