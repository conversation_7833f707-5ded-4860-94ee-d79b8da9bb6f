import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../utils/api';

const initialState = {
  downline: null,
  commissions: [],
  mlmConfig: null,
  commissionAuditLogs: [],
  userCommissions: [],
  userWallet: null,
  allUsers: [],
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: '',
};

// Get user downline
export const getUserDownline = createAsyncThunk(
  'mlm/getUserDownline',
  async (_, thunkAPI) => {
    try {
      // Debug: Log the API request
      console.log('getUserDownline - Making API request');

      // The API interceptor will automatically add the token from localStorage
      const response = await api.get('/users/downline');
      console.log('getUserDownline - API response:', response.data);

      // Check if the response has the expected structure
      if (response.data && response.data.data) {
        return response.data;
      } else {
        console.log('getUserDownline - Unexpected response structure:', response.data);
        return { data: {} };
      }
    } catch (error) {
      console.log('getUserDownline - API error:', error);

      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get MLM configuration
export const getMlmConfig = createAsyncThunk(
  'mlm/getConfig',
  async (_, thunkAPI) => {
    try {
      // Debug: Log the API request
      console.log('getMlmConfig - Making API request');

      // Get user from state
      const user = thunkAPI.getState().auth.user;
      console.log('getMlmConfig - User from state:', user);

      if (!user || !user.token) {
        console.log('getMlmConfig - No user or token available');
        return thunkAPI.rejectWithValue('Authentication token not found');
      }

      // Use the correct API endpoint with the API utility
      // Note: The baseURL already includes '/api', so we don't need to include it here
      const response = await api.get('/mlm/config');
      console.log('getMlmConfig - API response:', response.data);

      return response.data;
    } catch (error) {
      console.log('getMlmConfig - API error:', error);

      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get user commissions
export const getUserCommissions = createAsyncThunk(
  'mlm/getUserCommissions',
  async (_, thunkAPI) => {
    try {
      // Debug: Log the API request
      console.log('getUserCommissions - Making API request');

      // Get user from state
      const user = thunkAPI.getState().auth.user;
      console.log('getUserCommissions - User from state:', user);

      if (!user || !user.token) {
        console.log('getUserCommissions - No user or token available');
        return thunkAPI.rejectWithValue('Authentication token not found');
      }

      // This endpoint would need to be implemented in the backend
      // Use the correct API endpoint with the API utility
      // Note: The baseURL already includes '/api', so we don't need to include it here
      const response = await api.get('/mlm/commissions');
      console.log('getUserCommissions - API response:', response.data);

      return response.data;
    } catch (error) {
      console.log('getUserCommissions - API error:', error);

      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Update MLM configuration
export const updateMlmConfig = createAsyncThunk(
  'mlm/updateConfig',
  async (configData, thunkAPI) => {
    try {
      // Debug: Log the API request
      console.log('updateMlmConfig - Making API request with data:', configData);

      // Get user from state
      const user = thunkAPI.getState().auth.user;
      console.log('updateMlmConfig - User from state:', user);

      if (!user || !user.token) {
        console.log('updateMlmConfig - No user or token available');
        return thunkAPI.rejectWithValue('Authentication token not found');
      }

      const token = user.token;

      // Use the correct API endpoint with the API utility
      // Note: The baseURL already includes '/api', so we don't need to include it here
      const response = await api.put('/mlm/config', configData);
      console.log('updateMlmConfig - API response:', response.data);

      return response.data;
    } catch (error) {
      console.log('updateMlmConfig - API error:', error);

      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get all commissions (admin)
export const getAllCommissions = createAsyncThunk(
  'mlm/getAllCommissions',
  async (params = {}, thunkAPI) => {
    try {
      console.log('getAllCommissions - Making API request with params:', params);

      // Build query string
      const { page = 1, limit = 10, user, type, status, startDate, endDate } = params;
      let queryString = `/mlm/commissions?page=${page}&limit=${limit}`;

      if (user) {
        queryString += `&user=${user}`;
      }

      if (type) {
        queryString += `&type=${type}`;
      }

      if (status) {
        queryString += `&status=${status}`;
      }

      if (startDate) {
        queryString += `&startDate=${startDate}`;
      }

      if (endDate) {
        queryString += `&endDate=${endDate}`;
      }

      const response = await api.get(queryString);
      console.log('getAllCommissions - API response:', response.data);

      return response.data;
    } catch (error) {
      console.log('getAllCommissions - API error:', error);

      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get user commissions by ID (admin)
export const getUserCommissionsById = createAsyncThunk(
  'mlm/getUserCommissionsById',
  async ({ userId, params = {} }, thunkAPI) => {
    try {
      console.log('getUserCommissionsById - Making API request for user:', userId);

      // Build query string
      const { page = 1, limit = 10, type, status, startDate, endDate } = params;
      let queryString = `/mlm/commissions/user/${userId}?page=${page}&limit=${limit}`;

      if (type) {
        queryString += `&type=${type}`;
      }

      if (status) {
        queryString += `&status=${status}`;
      }

      if (startDate) {
        queryString += `&startDate=${startDate}`;
      }

      if (endDate) {
        queryString += `&endDate=${endDate}`;
      }

      const response = await api.get(queryString);
      console.log('getUserCommissionsById - API response:', response.data);

      return response.data;
    } catch (error) {
      console.log('getUserCommissionsById - API error:', error);

      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Create commission
export const createCommission = createAsyncThunk(
  'mlm/createCommission',
  async (commissionData, thunkAPI) => {
    try {
      console.log('createCommission - Making API request with data:', commissionData);

      const response = await api.post('/mlm/commissions', commissionData);
      console.log('createCommission - API response:', response.data);

      return response.data;
    } catch (error) {
      console.log('createCommission - API error:', error);

      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Update commission
export const updateCommission = createAsyncThunk(
  'mlm/updateCommission',
  async ({ id, commissionData }, thunkAPI) => {
    try {
      console.log('updateCommission - Making API request with data:', commissionData);

      const response = await api.put(`/mlm/commissions/${id}`, commissionData);
      console.log('updateCommission - API response:', response.data);

      return response.data;
    } catch (error) {
      console.log('updateCommission - API error:', error);

      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get commission audit logs
export const getCommissionAuditLogs = createAsyncThunk(
  'mlm/getCommissionAuditLogs',
  async (params = {}, thunkAPI) => {
    try {
      console.log('getCommissionAuditLogs - Making API request with params:', params);

      // Build query string
      const { page = 1, limit = 10, user, admin, actionType, startDate, endDate } = params;
      let queryString = `/mlm/commissions/audit?page=${page}&limit=${limit}`;

      if (user) {
        queryString += `&user=${user}`;
      }

      if (admin) {
        queryString += `&admin=${admin}`;
      }

      if (actionType) {
        queryString += `&actionType=${actionType}`;
      }

      if (startDate) {
        queryString += `&startDate=${startDate}`;
      }

      if (endDate) {
        queryString += `&endDate=${endDate}`;
      }

      const response = await api.get(queryString);
      console.log('getCommissionAuditLogs - API response:', response.data);

      return response.data;
    } catch (error) {
      console.log('getCommissionAuditLogs - API error:', error);

      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Adjust user wallet
export const adjustWallet = createAsyncThunk(
  'mlm/adjustWallet',
  async (adjustmentData, thunkAPI) => {
    try {
      console.log('adjustWallet - Making API request with data:', adjustmentData);

      const response = await api.post('/mlm/commissions/wallet-adjustment', adjustmentData);
      console.log('adjustWallet - API response:', response.data);

      return response.data;
    } catch (error) {
      console.log('adjustWallet - API error:', error);

      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const mlmSlice = createSlice({
  name: 'mlm',
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = '';
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getUserDownline.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getUserDownline.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.downline = action.payload.data;
      })
      .addCase(getUserDownline.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(getMlmConfig.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getMlmConfig.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.mlmConfig = action.payload.data;
      })
      .addCase(getMlmConfig.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(getUserCommissions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getUserCommissions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.commissions = action.payload.data;
      })
      .addCase(getUserCommissions.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(updateMlmConfig.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateMlmConfig.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.mlmConfig = action.payload.data;
      })
      .addCase(updateMlmConfig.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      // Get all commissions
      .addCase(getAllCommissions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllCommissions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.commissions = action.payload.data;
      })
      .addCase(getAllCommissions.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      // Get user commissions by ID
      .addCase(getUserCommissionsById.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getUserCommissionsById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.userCommissions = action.payload.data;
        state.userWallet = action.payload.wallet;
      })
      .addCase(getUserCommissionsById.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      // Create commission
      .addCase(createCommission.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createCommission.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.message = 'Commission created successfully';
      })
      .addCase(createCommission.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      // Update commission
      .addCase(updateCommission.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateCommission.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.message = 'Commission updated successfully';
      })
      .addCase(updateCommission.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      // Get commission audit logs
      .addCase(getCommissionAuditLogs.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getCommissionAuditLogs.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.commissionAuditLogs = action.payload.data;
      })
      .addCase(getCommissionAuditLogs.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      // Adjust wallet
      .addCase(adjustWallet.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(adjustWallet.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.message = 'Wallet adjusted successfully';
      })
      .addCase(adjustWallet.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      });
  },
});

export const { reset } = mlmSlice.actions;
export default mlmSlice.reducer;
