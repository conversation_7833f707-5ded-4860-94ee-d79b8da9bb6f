import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../utils/api';

const initialState = {
  orders: [],
  order: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: '',
};

// Create order
export const createOrder = createAsyncThunk(
  'orders/create',
  async (orderData, thunkAPI) => {
    try {
      console.log('createOrder - Making API request with data:', orderData);

      // Ensure all order items have valid product IDs
      const validOrderData = {
        ...orderData,
        orderItems: orderData.orderItems.filter(item => item.product)
      };

      if (validOrderData.orderItems.length === 0) {
        throw new Error('No valid products in order');
      }

      // The API interceptor will automatically add the token from localStorage
      const response = await api.post('/orders', validOrderData);
      console.log('createOrder - API response:', response.data);

      return response.data;
    } catch (error) {
      console.error('createOrder - API error:', error);

      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get order details
export const getOrderDetails = createAsyncThunk(
  'orders/getDetails',
  async (id, thunkAPI) => {
    try {
      const response = await api.get(`/orders/${id}`);
      return response.data;
    } catch (error) {
      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Pay order
export const payOrder = createAsyncThunk(
  'orders/pay',
  async ({ orderId, paymentResult }, thunkAPI) => {
    try {
      const response = await api.put(`/orders/${orderId}/pay`, paymentResult);
      return response.data;
    } catch (error) {
      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get my orders
export const getMyOrders = createAsyncThunk(
  'orders/getMyOrders',
  async (_, thunkAPI) => {
    try {
      console.log('getMyOrders - Making API request');
      const response = await api.get('/orders/myorders');
      console.log('getMyOrders - API response:', response.data);

      // Check if the response has the expected structure
      if (response.data && response.data.data) {
        return response.data;
      } else {
        console.log('getMyOrders - Unexpected response structure:', response.data);
        return { data: [] };
      }
    } catch (error) {
      console.log('getMyOrders - API error:', error);

      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get all orders (admin)
export const getOrders = createAsyncThunk(
  'orders/getAll',
  async (params = {}, thunkAPI) => {
    try {
      console.log('getOrders - Making API request with params:', params);

      // Build query string
      const { page = 1, limit = 10, status, approvalStatus } = params;
      let queryString = `/orders?page=${page}&limit=${limit}`;

      if (status) {
        queryString += `&status=${status}`;
      }

      if (approvalStatus) {
        queryString += `&approvalStatus=${approvalStatus}`;
      }

      const response = await api.get(queryString);
      console.log('getOrders - API response:', response.data);

      return response.data;
    } catch (error) {
      console.error('getOrders - API error:', error);

      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Approve order
export const approveOrder = createAsyncThunk(
  'orders/approve',
  async (id, thunkAPI) => {
    try {
      console.log('approveOrder - Making API request for order:', id);
      const response = await api.put(`/orders/${id}/approve`);
      console.log('approveOrder - API response:', response.data);

      return response.data;
    } catch (error) {
      console.error('approveOrder - API error:', error);

      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Reject order
export const rejectOrder = createAsyncThunk(
  'orders/reject',
  async ({ id, reason }, thunkAPI) => {
    try {
      console.log('rejectOrder - Making API request for order:', id);
      const response = await api.put(`/orders/${id}/reject`, { reason });
      console.log('rejectOrder - API response:', response.data);

      return response.data;
    } catch (error) {
      console.error('rejectOrder - API error:', error);

      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Approve payment
export const approvePayment = createAsyncThunk(
  'orders/approvePayment',
  async ({ id, note }, thunkAPI) => {
    try {
      console.log('approvePayment - Making API request for order:', id);
      const response = await api.put(`/orders/${id}/approve-payment`, { note });
      console.log('approvePayment - API response:', response.data);

      return response.data;
    } catch (error) {
      console.error('approvePayment - API error:', error);

      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Reject payment
export const rejectPayment = createAsyncThunk(
  'orders/rejectPayment',
  async ({ id, reason }, thunkAPI) => {
    try {
      console.log('rejectPayment - Making API request for order:', id);
      const response = await api.put(`/orders/${id}/reject-payment`, { reason });
      console.log('rejectPayment - API response:', response.data);

      return response.data;
    } catch (error) {
      console.error('rejectPayment - API error:', error);

      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Update order status
export const updateOrderStatus = createAsyncThunk(
  'orders/updateStatus',
  async ({ id, status, note }, thunkAPI) => {
    try {
      console.log('updateOrderStatus - Making API request for order:', id);
      const response = await api.put(`/orders/${id}/status`, { status, note });
      console.log('updateOrderStatus - API response:', response.data);

      return response.data;
    } catch (error) {
      console.error('updateOrderStatus - API error:', error);

      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Update delivery tracking
export const updateDeliveryTracking = createAsyncThunk(
  'orders/updateTracking',
  async ({ id, trackingNumber, carrier, estimatedDeliveryDate, trackingUrl }, thunkAPI) => {
    try {
      console.log('updateDeliveryTracking - Making API request for order:', id);
      const response = await api.put(`/orders/${id}/tracking`, {
        trackingNumber,
        carrier,
        estimatedDeliveryDate,
        trackingUrl
      });
      console.log('updateDeliveryTracking - API response:', response.data);

      return response.data;
    } catch (error) {
      console.error('updateDeliveryTracking - API error:', error);

      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const orderSlice = createSlice({
  name: 'orders',
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = '';
    },
    resetOrder: (state) => {
      state.order = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(createOrder.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createOrder.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.order = action.payload.data;
      })
      .addCase(createOrder.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(getOrderDetails.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getOrderDetails.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.order = action.payload.data;
      })
      .addCase(getOrderDetails.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(payOrder.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(payOrder.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.order = action.payload.data;
      })
      .addCase(payOrder.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(getMyOrders.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getMyOrders.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.orders = action.payload.data;
      })
      .addCase(getMyOrders.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(getOrders.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getOrders.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.orders = action.payload.data;
        state.page = action.payload.page;
        state.pages = action.payload.pages;
        state.total = action.payload.total;
      })
      .addCase(getOrders.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(approveOrder.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(approveOrder.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        // Update the order in the orders array if it exists
        const index = state.orders.findIndex(
          (order) => order._id === action.payload.data._id
        );
        if (index !== -1) {
          state.orders[index] = action.payload.data;
        }
        // Update the current order if it matches
        if (state.order && state.order._id === action.payload.data._id) {
          state.order = action.payload.data;
        }
      })
      .addCase(approveOrder.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(rejectOrder.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(rejectOrder.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        // Update the order in the orders array if it exists
        const index = state.orders.findIndex(
          (order) => order._id === action.payload.data._id
        );
        if (index !== -1) {
          state.orders[index] = action.payload.data;
        }
        // Update the current order if it matches
        if (state.order && state.order._id === action.payload.data._id) {
          state.order = action.payload.data;
        }
      })
      .addCase(rejectOrder.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(approvePayment.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(approvePayment.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.message = 'Payment approved successfully';
        // Update the order in the orders array if it exists
        const index = state.orders.findIndex(
          (order) => order._id === action.payload.data._id
        );
        if (index !== -1) {
          state.orders[index] = action.payload.data;
        }
        // Update the current order if it matches
        if (state.order && state.order._id === action.payload.data._id) {
          state.order = action.payload.data;
        }
      })
      .addCase(approvePayment.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(rejectPayment.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(rejectPayment.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.message = 'Payment rejected successfully';
        // Update the order in the orders array if it exists
        const index = state.orders.findIndex(
          (order) => order._id === action.payload.data._id
        );
        if (index !== -1) {
          state.orders[index] = action.payload.data;
        }
        // Update the current order if it matches
        if (state.order && state.order._id === action.payload.data._id) {
          state.order = action.payload.data;
        }
      })
      .addCase(rejectPayment.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(updateOrderStatus.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateOrderStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.message = 'Order status updated successfully';
        // Update the order in the orders array if it exists
        const index = state.orders.findIndex(
          (order) => order._id === action.payload.data._id
        );
        if (index !== -1) {
          state.orders[index] = action.payload.data;
        }
        // Update the current order if it matches
        if (state.order && state.order._id === action.payload.data._id) {
          state.order = action.payload.data;
        }
      })
      .addCase(updateOrderStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(updateDeliveryTracking.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateDeliveryTracking.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.message = 'Delivery tracking updated successfully';
        // Update the order in the orders array if it exists
        const index = state.orders.findIndex(
          (order) => order._id === action.payload.data._id
        );
        if (index !== -1) {
          state.orders[index] = action.payload.data;
        }
        // Update the current order if it matches
        if (state.order && state.order._id === action.payload.data._id) {
          state.order = action.payload.data;
        }
      })
      .addCase(updateDeliveryTracking.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      });
  },
});

export const { reset, resetOrder } = orderSlice.actions;
export default orderSlice.reducer;
