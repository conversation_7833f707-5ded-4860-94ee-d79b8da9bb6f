const mongoose = require('mongoose');
const User = require('./models/user.model');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI)
  .then(() => console.log('MongoDB Connected'))
  .catch(err => console.error('MongoDB Connection Error:', err));

// Create test users
const createTestUsers = async () => {
  try {
    // First, create a root admin user
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    
    if (!adminUser) {
      console.log('Creating admin user...');
      const admin = await User.create({
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'admin123',
        phone: '1234567890',
        role: 'admin',
        address: {
          street: '123 Admin St',
          city: 'Admin City',
          state: 'Admin State',
          postalCode: '12345',
          country: 'Admin Country'
        }
      });
      
      console.log('Admin user created:', admin.email, 'with referral code:', admin.referralCode);
    } else {
      console.log('Admin user already exists with referral code:', adminUser.referralCode);
    }
    
    // Get the admin user (either newly created or existing)
    const admin = await User.findOne({ email: '<EMAIL>' });
    
    // Create a franchise user using the admin's referral code
    const franchiseUser = await User.findOne({ email: '<EMAIL>' });
    
    if (!franchiseUser) {
      console.log('Creating franchise user...');
      
      // Check if admin already has a left leg
      if (admin.leftLeg) {
        console.log('Admin already has a left leg, cannot add franchise user there');
        return;
      }
      
      const franchise = await User.create({
        name: 'Franchise User',
        email: '<EMAIL>',
        password: 'franchise123',
        phone: '9876543210',
        role: 'franchise',
        referrer: admin._id,
        position: 'left',
        address: {
          street: '456 Franchise St',
          city: 'Franchise City',
          state: 'Franchise State',
          postalCode: '54321',
          country: 'Franchise Country'
        }
      });
      
      // Update admin's left leg
      await User.findByIdAndUpdate(admin._id, { leftLeg: franchise._id });
      
      console.log('Franchise user created:', franchise.email, 'with referral code:', franchise.referralCode);
    } else {
      console.log('Franchise user already exists with referral code:', franchiseUser.referralCode);
    }
    
    // Create a regular user
    const regularUser = await User.findOne({ email: '<EMAIL>' });
    
    if (!regularUser) {
      console.log('Creating regular user...');
      
      // Check if admin already has a right leg
      if (admin.rightLeg) {
        console.log('Admin already has a right leg, cannot add regular user there');
        return;
      }
      
      const user = await User.create({
        name: 'Regular User',
        email: '<EMAIL>',
        password: 'user123',
        phone: '5555555555',
        referrer: admin._id,
        position: 'right',
        address: {
          street: '789 User St',
          city: 'User City',
          state: 'User State',
          postalCode: '98765',
          country: 'User Country'
        }
      });
      
      // Update admin's right leg
      await User.findByIdAndUpdate(admin._id, { rightLeg: user._id });
      
      console.log('Regular user created:', user.email, 'with referral code:', user.referralCode);
    } else {
      console.log('Regular user already exists with referral code:', regularUser.referralCode);
    }
    
    console.log('Test users created successfully!');
    
  } catch (error) {
    console.error('Error creating test users:', error);
  } finally {
    mongoose.disconnect();
  }
};

// Run the function
createTestUsers();
