import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { FaArrowLeft, FaFilter, FaCalendarAlt, FaRupeeSign, FaUser, FaUserShield } from 'react-icons/fa';
import Spinner from '../../components/Spinner';
import { getCommissionAuditLogs, reset } from '../../features/mlm/mlmSlice';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

const CommissionAuditLogs = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const { commissionAuditLogs, isLoading, isError, message } = useSelector((state) => state.mlm);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalLogs, setTotalLogs] = useState(0);

  // Filter state
  const [filters, setFilters] = useState({
    user: '',
    admin: '',
    actionType: '',
    startDate: null,
    endDate: null,
  });

  // Show filter panel state
  const [showFilters, setShowFilters] = useState(false);

  // Load audit logs on component mount
  useEffect(() => {
    loadAuditLogs();
  }, [currentPage]);

  // Handle error messages
  useEffect(() => {
    if (isError) {
      toast.error(message);
    }

    dispatch(reset());
  }, [isError, message, dispatch]);

  // Load audit logs with current filters and pagination
  const loadAuditLogs = () => {
    const params = {
      page: currentPage,
      limit: 10,
      ...filters,
      startDate: filters.startDate ? filters.startDate.toISOString() : undefined,
      endDate: filters.endDate ? filters.endDate.toISOString() : undefined,
    };

    dispatch(getCommissionAuditLogs(params))
      .unwrap()
      .then((data) => {
        setTotalPages(data.pages);
        setTotalLogs(data.total);
      })
      .catch((error) => {
        console.error('Error loading audit logs:', error);
      });
  };

  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters((prev) => ({ ...prev, [name]: value }));
  };

  // Handle date filter changes
  const handleDateChange = (date, field) => {
    setFilters((prev) => ({ ...prev, [field]: date }));
  };

  // Apply filters
  const applyFilters = (e) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page when applying filters
    loadAuditLogs();
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      user: '',
      admin: '',
      actionType: '',
      startDate: null,
      endDate: null,
    });
    setCurrentPage(1);
    loadAuditLogs();
  };

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Format currency for display
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 2,
    }).format(amount);
  };

  // Handle pagination
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Get action type badge color
  const getActionTypeColor = (actionType) => {
    switch (actionType) {
      case 'create':
        return 'bg-green-100 text-green-800';
      case 'update':
        return 'bg-blue-100 text-blue-800';
      case 'delete':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return <Spinner />;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <button
          onClick={() => navigate('/admin/commissions')}
          className="mr-4 text-blue-600 hover:text-blue-800"
        >
          <FaArrowLeft className="inline mr-1" /> Back to Commissions
        </button>
        <h1 className="text-2xl font-bold">Commission Audit Logs</h1>
      </div>

      {/* Filter Panel */}
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">
            <FaFilter className="inline mr-2" /> Filters
          </h2>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="text-blue-600 hover:text-blue-800"
          >
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </button>
        </div>

        {showFilters && (
          <form onSubmit={applyFilters}>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <label htmlFor="user" className="block text-sm font-medium text-gray-700 mb-1">
                  <FaUser className="inline mr-1" /> User ID/Name
                </label>
                <input
                  type="text"
                  id="user"
                  name="user"
                  value={filters.user}
                  onChange={handleFilterChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Search by user ID or name"
                />
              </div>

              <div>
                <label htmlFor="admin" className="block text-sm font-medium text-gray-700 mb-1">
                  <FaUserShield className="inline mr-1" /> Admin ID/Name
                </label>
                <input
                  type="text"
                  id="admin"
                  name="admin"
                  value={filters.admin}
                  onChange={handleFilterChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Search by admin ID or name"
                />
              </div>

              <div>
                <label htmlFor="actionType" className="block text-sm font-medium text-gray-700 mb-1">
                  Action Type
                </label>
                <select
                  id="actionType"
                  name="actionType"
                  value={filters.actionType}
                  onChange={handleFilterChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Actions</option>
                  <option value="create">Create</option>
                  <option value="update">Update</option>
                  <option value="delete">Delete</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <FaCalendarAlt className="inline mr-1" /> Start Date
                </label>
                <DatePicker
                  selected={filters.startDate}
                  onChange={(date) => handleDateChange(date, 'startDate')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholderText="Select start date"
                  dateFormat="dd/MM/yyyy"
                  isClearable
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <FaCalendarAlt className="inline mr-1" /> End Date
                </label>
                <DatePicker
                  selected={filters.endDate}
                  onChange={(date) => handleDateChange(date, 'endDate')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholderText="Select end date"
                  dateFormat="dd/MM/yyyy"
                  isClearable
                  minDate={filters.startDate}
                />
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <button
                type="button"
                onClick={resetFilters}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100"
              >
                Reset
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Apply Filters
              </button>
            </div>
          </form>
        )}
      </div>

      {/* Audit Logs Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold">
            Audit Logs ({totalLogs})
          </h2>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date & Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Admin
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Action
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Wallet Impact
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {commissionAuditLogs && commissionAuditLogs.length > 0 ? (
                commissionAuditLogs.map((log) => (
                  <tr key={log._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(log.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {log.user?.name || 'Unknown User'}
                          </div>
                          <div className="text-sm text-gray-500">
                            {log.user?.email || 'No email'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {log.admin?.name || 'Unknown Admin'}
                          </div>
                          <div className="text-sm text-gray-500">
                            {log.admin?.email || 'No email'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getActionTypeColor(
                          log.actionType
                        )}`}
                      >
                        {log.actionType}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div>
                        <p><strong>Reason:</strong> {log.reason}</p>
                        {log.actionType === 'update' && (
                          <div className="mt-1 text-xs">
                            <p>
                              <strong>Changed:</strong>{' '}
                              {log.previousValues.amount !== log.newValues.amount && 'Amount, '}
                              {log.previousValues.status !== log.newValues.status && 'Status, '}
                              {log.previousValues.description !== log.newValues.description && 'Description'}
                            </p>
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {log.walletAdjustment ? (
                        <div className="text-sm">
                          <p className={log.walletAdjustment.amount >= 0 ? 'text-green-600' : 'text-red-600'}>
                            <FaRupeeSign className="inline text-xs" />{' '}
                            {log.walletAdjustment.amount >= 0 ? '+' : ''}
                            {formatCurrency(log.walletAdjustment.amount)}
                          </p>
                          <p className="text-xs text-gray-500">
                            Balance: {formatCurrency(log.walletAdjustment.newBalance)}
                          </p>
                        </div>
                      ) : (
                        <span className="text-gray-400">No impact</span>
                      )}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="6" className="px-6 py-4 text-center text-gray-500">
                    No audit logs found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 flex items-center justify-between border-t border-gray-200">
            <div>
              <p className="text-sm text-gray-700">
                Showing page <span className="font-medium">{currentPage}</span> of{' '}
                <span className="font-medium">{totalPages}</span>
              </p>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className={`px-3 py-1 rounded-md ${
                  currentPage === 1
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Previous
              </button>
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className={`px-3 py-1 rounded-md ${
                  currentPage === totalPages
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CommissionAuditLogs;
