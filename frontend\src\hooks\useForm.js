import { useState } from 'react';

const useForm = (initialState = {}, validateForm = () => ({}), onSubmit = () => {}) => {
  const [values, setValues] = useState(initialState);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  // Handle input change
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    // Handle checkboxes
    const inputValue = type === 'checkbox' ? checked : value;

    setValues({
      ...values,
      [name]: inputValue,
    });

    // Mark field as touched
    if (!touched[name]) {
      setTouched({
        ...touched,
        [name]: true,
      });
    }

    // Validate on change
    const validationErrors = validateForm({
      ...values,
      [name]: inputValue,
    });

    setErrors(validationErrors);
  };

  // Handle blur event
  const handleBlur = (e) => {
    const { name } = e.target;

    // Mark field as touched
    setTouched({
      ...touched,
      [name]: true,
    });

    // Validate on blur
    const validationErrors = validateForm(values);
    setErrors(validationErrors);
  };

  // Reset form
  const resetForm = () => {
    setValues(initialState);
    setErrors({});
    setTouched({});
  };

  // Set form values
  const setFormValues = (newValues) => {
    setValues(newValues);
  };

  // Check if form is valid
  const isValid = Object.keys(errors).length === 0;

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Mark all fields as touched
    const allTouched = {};
    Object.keys(values).forEach(key => {
      allTouched[key] = true;
    });
    setTouched(allTouched);

    // Validate all fields
    const validationErrors = validateForm(values);
    setErrors(validationErrors);

    // If no errors, call onSubmit
    if (Object.keys(validationErrors).length === 0) {
      // Make sure onSubmit is a function before calling it
      if (typeof onSubmit === 'function') {
        onSubmit(values);
      } else {
        console.error('onSubmit is not a function');
      }
    }
  };

  return {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    resetForm,
    setFormValues,
    setValues, // Add direct access to setValues
    handleSubmit,
    isValid,
  };
};

export default useForm;
