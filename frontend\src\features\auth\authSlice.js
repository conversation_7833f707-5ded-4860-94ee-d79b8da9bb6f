import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../utils/api';

// Get user from localStorage
const user = JSON.parse(localStorage.getItem('user'));

const initialState = {
  user: user || null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: '',
};

// Register user
export const register = createAsyncThunk(
  'auth/register',
  async (userData, thunkAPI) => {
    try {
      const response = await api.post('/auth/register', userData);

      if (response.data) {
        localStorage.setItem('user', JSON.stringify(response.data));
      }

      return response.data;
    } catch (error) {
      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Login user
export const login = createAsyncThunk(
  'auth/login',
  async (userData, thunkAPI) => {
    try {
      // Debug: Log the login attempt
      console.log('login - Attempting login with:', { email: userData.email });

      // Use the correct API endpoint with the API utility
      // Note: The baseURL already includes '/api', so we don't need to include it here
      const response = await api.post('/auth/login', userData);
      console.log('login - API response:', response.data);

      if (response.data) {
        // Store user data in localStorage
        localStorage.setItem('user', JSON.stringify(response.data));

        // Log the stored user data
        console.log('login - User data stored in localStorage:', response.data);
        console.log('login - User role:', response.data.role);
        console.log('login - Is admin:', response.data.isAdmin);
      }

      return response.data;
    } catch (error) {
      console.log('login - API error:', error);

      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Logout user
export const logout = createAsyncThunk('auth/logout', async () => {
  console.log('logout - Removing user from localStorage');
  localStorage.removeItem('user');

  console.log('logout - Making API request to logout');
  try {
    // Note: The baseURL already includes '/api', so we don't need to include it here
    await api.get('/auth/logout');
    console.log('logout - Successfully logged out');
  } catch (error) {
    console.log('logout - Error during logout:', error);
  }
});

// Get user profile
export const getUserProfile = createAsyncThunk(
  'auth/getUserProfile',
  async (_, thunkAPI) => {
    try {
      // Debug: Log the API request
      console.log('getUserProfile - Making API request');

      // The API interceptor will automatically add the token from localStorage
      const response = await api.get('/auth/me');
      console.log('getUserProfile - API response:', response.data);

      // Check if the response has the expected structure
      if (response.data && response.data.data) {
        return response.data;
      } else {
        console.log('getUserProfile - Unexpected response structure:', response.data);
        return { data: {} };
      }
    } catch (error) {
      console.log('getUserProfile - API error:', error);

      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Update user profile
export const updateProfile = createAsyncThunk(
  'auth/updateProfile',
  async (userData, thunkAPI) => {
    try {
      console.log('updateProfile - Making API request with data:', userData);

      // The API interceptor will automatically add the token from localStorage
      const response = await api.put('/auth/updateprofile', userData);
      console.log('updateProfile - API response:', response.data);

      // Check if the response has the expected structure
      if (response.data && response.data.data) {
        return response.data;
      } else {
        console.log('updateProfile - Unexpected response structure:', response.data);
        return { data: {} };
      }
    } catch (error) {
      console.log('updateProfile - API error:', error);

      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = '';
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(register.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(register.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.user = action.payload;
      })
      .addCase(register.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        state.user = null;
      })
      .addCase(login.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.user = action.payload;
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        state.user = null;
      })
      .addCase(logout.fulfilled, (state) => {
        state.user = null;
      })
      .addCase(getUserProfile.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getUserProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;

        // Log the user profile data
        console.log('getUserProfile.fulfilled - User profile data:', action.payload.data);

        // Make sure we preserve the token and other auth data
        state.user = {
          ...state.user,
          ...action.payload.data,
          // Ensure PV values are properly set
          leftPV: action.payload.data.leftPV || 0,
          rightPV: action.payload.data.rightPV || 0,
          personalPV: action.payload.data.personalPV || 0
        };

        console.log('getUserProfile.fulfilled - Updated user state:', state.user);
      })
      .addCase(getUserProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(updateProfile.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;

        // Log the updated profile data
        console.log('updateProfile.fulfilled - Updated profile data:', action.payload.data);

        // Update the user state with the new profile data
        state.user = {
          ...state.user,
          ...action.payload.data,
          // Ensure PV values are properly set
          leftPV: action.payload.data.leftPV || 0,
          rightPV: action.payload.data.rightPV || 0,
          personalPV: action.payload.data.personalPV || 0
        };

        console.log('updateProfile.fulfilled - Updated user state:', state.user);
      })
      .addCase(updateProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      });
  },
});

export const { reset } = authSlice.actions;
export default authSlice.reducer;
