import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { FaSave, FaArrowLeft, FaTrash } from 'react-icons/fa';
import { getProductDetails, updateProduct, reset, clearProductState } from '../../features/products/productSlice';
import Spinner from '../../components/Spinner';
import useForm from '../../hooks/useForm';

const ProductEdit = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { product, isLoading, isError, isSuccess, message } = useSelector(
    (state) => state.products
  );
  const { user } = useSelector((state) => state.auth);

  // Categories
  const categories = [
    'Electronics',
    'Clothing',
    'Health & Wellness',
    'Home & Kitchen',
    'Beauty',
    'Sports',
  ];

  // Initial form state
  const [initialState, setInitialState] = useState({
    name: '',
    description: '',
    price: '',
    pointValue: '',
    category: '',
    subcategory: '',
    stock: '',
    featured: false,
    images: [{ public_id: 'placeholder', url: 'https://via.placeholder.com/300' }],
  });

  // Form validation
  const validateForm = (values) => {
    const errors = {};

    if (!values.name) {
      errors.name = 'Product name is required';
    } else if (values.name.length > 100) {
      errors.name = 'Product name cannot exceed 100 characters';
    }

    if (!values.description) {
      errors.description = 'Product description is required';
    }

    if (!values.price) {
      errors.price = 'Price is required';
    } else if (isNaN(values.price) || Number(values.price) < 0) {
      errors.price = 'Price must be a positive number';
    }

    if (!values.pointValue) {
      errors.pointValue = 'Point value is required';
    } else if (isNaN(values.pointValue) || Number(values.pointValue) < 0) {
      errors.pointValue = 'Point value must be a positive number';
    }

    if (!values.category) {
      errors.category = 'Category is required';
    }

    if (!values.stock) {
      errors.stock = 'Stock is required';
    } else if (isNaN(values.stock) || Number(values.stock) < 0) {
      errors.stock = 'Stock must be a non-negative number';
    }

    return errors;
  };

  // Define onSubmit function before using it in useForm
  const onSubmit = (formValues) => {
    // Convert string values to numbers
    const productData = {
      ...formValues,
      price: Number(formValues.price),
      pointValue: Number(formValues.pointValue),
      stock: Number(formValues.stock),
    };

    dispatch(updateProduct({ id, productData }));
  };

  // Use custom form hook
  const { values, errors, touched, handleChange, handleSubmit, setValues } = useForm(
    initialState,
    validateForm,
    onSubmit
  );

  // Handle image upload (placeholder for now)
  const handleImageUpload = (e) => {
    // In a real implementation, this would upload to a service like Cloudinary
    // For now, we'll just use a placeholder
    toast.info('Image upload functionality would be implemented here');
  };

  useEffect(() => {
    // Check for admin role
    const userRole = user?.role || (user?.data && user.data.role);
    const isUserAdmin = userRole === 'admin' || user?.isAdmin === true;

    if (!user || !isUserAdmin) {
      navigate('/login');
      return;
    }

    // Fetch product details
    dispatch(getProductDetails(id));

    if (isError) {
      toast.error(message);
    }

    if (isSuccess && message === '') {
      toast.success('Product updated successfully');
      navigate('/admin/products');
      dispatch(reset());
    }

    return () => {
      dispatch(clearProductState());
    };
  }, [user, navigate, isError, isSuccess, message, dispatch, id]);

  // Update form values when product data is loaded
  useEffect(() => {
    if (product) {
      const productData = {
        name: product.name || '',
        description: product.description || '',
        price: product.price || '',
        pointValue: product.pointValue || '',
        category: product.category || '',
        subcategory: product.subcategory || '',
        stock: product.stock || '',
        featured: product.featured || false,
        images: product.images || [{ public_id: 'placeholder', url: 'https://via.placeholder.com/300' }],
      };

      setInitialState(productData);
      setValues(productData);
    }
  }, [product, setValues]);

  if (isLoading) {
    return <Spinner />;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Edit Product</h1>
        <button
          onClick={() => navigate('/admin/products')}
          className="bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md flex items-center"
        >
          <FaArrowLeft className="mr-2" /> Back to Products
        </button>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Product Name */}
            <div className="col-span-2">
              <label className="block text-gray-700 font-medium mb-2" htmlFor="name">
                Product Name*
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={values.name}
                onChange={handleChange}
                className={`w-full border ${
                  errors.name && touched.name ? 'border-red-500' : 'border-gray-300'
                } rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
              {errors.name && touched.name && (
                <p className="text-red-500 text-sm mt-1">{errors.name}</p>
              )}
            </div>

            {/* Price */}
            <div>
              <label className="block text-gray-700 font-medium mb-2" htmlFor="price">
                Price (₹)*
              </label>
              <input
                type="number"
                id="price"
                name="price"
                min="0"
                step="0.01"
                value={values.price}
                onChange={handleChange}
                className={`w-full border ${
                  errors.price && touched.price ? 'border-red-500' : 'border-gray-300'
                } rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
              {errors.price && touched.price && (
                <p className="text-red-500 text-sm mt-1">{errors.price}</p>
              )}
            </div>

            {/* Point Value */}
            <div>
              <label className="block text-gray-700 font-medium mb-2" htmlFor="pointValue">
                Point Value (PV)*
              </label>
              <input
                type="number"
                id="pointValue"
                name="pointValue"
                min="0"
                value={values.pointValue}
                onChange={handleChange}
                className={`w-full border ${
                  errors.pointValue && touched.pointValue ? 'border-red-500' : 'border-gray-300'
                } rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
              {errors.pointValue && touched.pointValue && (
                <p className="text-red-500 text-sm mt-1">{errors.pointValue}</p>
              )}
            </div>

            {/* Category */}
            <div>
              <label className="block text-gray-700 font-medium mb-2" htmlFor="category">
                Category*
              </label>
              <select
                id="category"
                name="category"
                value={values.category}
                onChange={handleChange}
                className={`w-full border ${
                  errors.category && touched.category ? 'border-red-500' : 'border-gray-300'
                } rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
              >
                <option value="">Select Category</option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
              {errors.category && touched.category && (
                <p className="text-red-500 text-sm mt-1">{errors.category}</p>
              )}
            </div>

            {/* Subcategory */}
            <div>
              <label className="block text-gray-700 font-medium mb-2" htmlFor="subcategory">
                Subcategory
              </label>
              <input
                type="text"
                id="subcategory"
                name="subcategory"
                value={values.subcategory}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Stock */}
            <div>
              <label className="block text-gray-700 font-medium mb-2" htmlFor="stock">
                Stock*
              </label>
              <input
                type="number"
                id="stock"
                name="stock"
                min="0"
                value={values.stock}
                onChange={handleChange}
                className={`w-full border ${
                  errors.stock && touched.stock ? 'border-red-500' : 'border-gray-300'
                } rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
              {errors.stock && touched.stock && (
                <p className="text-red-500 text-sm mt-1">{errors.stock}</p>
              )}
            </div>

            {/* Featured */}
            <div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="featured"
                  name="featured"
                  checked={values.featured}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="ml-2 block text-gray-700 font-medium" htmlFor="featured">
                  Featured Product
                </label>
              </div>
            </div>

            {/* Description */}
            <div className="col-span-2">
              <label className="block text-gray-700 font-medium mb-2" htmlFor="description">
                Description*
              </label>
              <textarea
                id="description"
                name="description"
                rows="4"
                value={values.description}
                onChange={handleChange}
                className={`w-full border ${
                  errors.description && touched.description ? 'border-red-500' : 'border-gray-300'
                } rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
              ></textarea>
              {errors.description && touched.description && (
                <p className="text-red-500 text-sm mt-1">{errors.description}</p>
              )}
            </div>

            {/* Image Upload */}
            <div className="col-span-2">
              <label className="block text-gray-700 font-medium mb-2">Product Images</label>
              <div className="border border-dashed border-gray-300 rounded-md p-4">
                <div className="flex items-center justify-center">
                  <button
                    type="button"
                    onClick={handleImageUpload}
                    className="bg-blue-50 hover:bg-blue-100 text-blue-600 py-2 px-4 rounded-md"
                  >
                    Upload Image
                  </button>
                </div>
                <p className="text-center text-gray-500 text-sm mt-2">
                  Note: Image upload functionality would be implemented in a real application
                </p>
              </div>
            </div>
          </div>

          <div className="mt-8 flex justify-end">
            <button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded-md flex items-center"
            >
              <FaSave className="mr-2" /> Update Product
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProductEdit;
