import { useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { FaMapMarkerAlt, FaCreditCard, FaShoppingBag, FaCheck, FaTimes } from 'react-icons/fa';
import { getOrderDetails, reset } from '../features/orders/orderSlice';
import Spinner from '../components/Spinner';

const Order = () => {
  const { id: orderId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { order, isLoading, isError, message } = useSelector((state) => state.orders);
  const { user } = useSelector((state) => state.auth);

  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }

    dispatch(getOrderDetails(orderId));

    if (isError) {
      toast.error(message);
    }

    return () => {
      dispatch(reset());
    };
  }, [dispatch, orderId, isError, message, navigate, user]);

  if (isLoading || !order) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <Spinner />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Order {order._id}</h1>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Order Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Shipping */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-lg font-semibold mb-4 flex items-center">
              <FaMapMarkerAlt className="text-blue-600 mr-2" /> Shipping
            </h2>
            <p className="text-gray-700">
              <strong>Name:</strong> {user.name}
            </p>
            <p className="text-gray-700">
              <strong>Email:</strong> {user.email}
            </p>
            <p className="text-gray-700">
              <strong>Address:</strong> {order.shippingAddress.address},{' '}
              {order.shippingAddress.city}, {order.shippingAddress.state},{' '}
              {order.shippingAddress.postalCode}, {order.shippingAddress.country}
            </p>
            <p className="text-gray-700">
              <strong>Phone:</strong> {order.shippingAddress.phone}
            </p>

            <div className="mt-4">
              {order.isDelivered ? (
                <div className="bg-green-100 text-green-800 p-3 rounded-md flex items-center">
                  <FaCheck className="mr-2" /> Delivered on {new Date(order.deliveredAt).toLocaleDateString()}
                </div>
              ) : (
                <div className="bg-red-100 text-red-800 p-3 rounded-md flex items-center">
                  <FaTimes className="mr-2" /> Not Delivered
                </div>
              )}
            </div>
          </div>

          {/* Payment Method */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-lg font-semibold mb-4 flex items-center">
              <FaCreditCard className="text-blue-600 mr-2" /> Payment Method
            </h2>
            <p className="text-gray-700">
              <strong>Method:</strong>{' '}
              {order.paymentMethod === 'razorpay'
                ? 'Razorpay (Credit/Debit Card, UPI, Netbanking)'
                : order.paymentMethod === 'wallet'
                ? 'Wallet Balance'
                : 'Cash on Delivery'}
            </p>

            <div className="mt-4">
              {order.isPaid ? (
                <div className="bg-green-100 text-green-800 p-3 rounded-md flex items-center">
                  <FaCheck className="mr-2" /> Paid on {new Date(order.paidAt).toLocaleDateString()}
                </div>
              ) : (
                <div className="bg-red-100 text-red-800 p-3 rounded-md flex items-center">
                  <FaTimes className="mr-2" /> Not Paid
                </div>
              )}
            </div>
          </div>

          {/* Order Items */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-lg font-semibold mb-4 flex items-center">
              <FaShoppingBag className="text-blue-600 mr-2" /> Order Items
            </h2>

            {order.orderItems.length === 0 ? (
              <div className="text-center py-4">
                <p className="text-gray-500">Order is empty</p>
              </div>
            ) : (
              <div className="divide-y">
                {order.orderItems.map((item, index) => (
                  <div key={index} className="py-4 flex items-center">
                    <div className="w-16 h-16 flex-shrink-0">
                      <img
                        src={item.image}
                        alt={item.name}
                        className="w-full h-full object-cover rounded-md"
                      />
                    </div>

                    <div className="ml-4 flex-grow">
                      <Link
                        to={`/product/${item.product}`}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        {item.name}
                      </Link>

                      {item.variant && (
                        <p className="text-sm text-gray-600">
                          {item.variant.name}: {item.variant.value}
                        </p>
                      )}

                      <p className="text-sm text-green-600">PV: {item.pointValue} × {item.qty} = {item.pointValue * item.qty}</p>
                    </div>

                    <div className="text-right">
                      <p className="font-semibold">
                        {item.qty} × ₹{item.price.toFixed(2)} = ₹{(item.qty * item.price).toFixed(2)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Order Summary */}
        <div>
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-lg font-semibold mb-4">Order Summary</h2>

            <div className="space-y-3 mb-6">
              <div className="flex justify-between">
                <span className="text-gray-600">Items</span>
                <span>₹{order.itemsPrice?.toFixed(2) || (order.totalPrice - order.shippingPrice - order.taxPrice).toFixed(2)}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">Shipping</span>
                <span>₹{order.shippingPrice?.toFixed(2) || '0.00'}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">Tax</span>
                <span>₹{order.taxPrice?.toFixed(2) || '0.00'}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">Total PV</span>
                <span className="text-green-600">{order.totalPointValue}</span>
              </div>

              <div className="border-t pt-3 mt-3">
                <div className="flex justify-between font-semibold">
                  <span>Total</span>
                  <span>₹{order.totalPrice.toFixed(2)}</span>
                </div>
              </div>
            </div>

            {!order.isPaid && order.paymentMethod === 'razorpay' && (
              <button
                type="button"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-md font-semibold mb-4"
                onClick={() => navigate(`/order/${order._id}/pay`)}
              >
                Pay Now
              </button>
            )}

            <div className="mt-4">
              <Link
                to="/dashboard"
                className="text-blue-600 hover:text-blue-800 block text-center"
              >
                Back to Dashboard
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Order;
