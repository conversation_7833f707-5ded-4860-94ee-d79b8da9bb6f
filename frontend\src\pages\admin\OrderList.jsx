import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { FaEye, FaCheck, FaTimes, FaFilter, FaSearch, FaCreditCard, FaMoneyBillWave, FaTruck, FaShippingFast } from 'react-icons/fa';
import {
  getOrders,
  approveOrder,
  rejectOrder,
  approvePayment,
  rejectPayment,
  updateOrderStatus,
  updateDeliveryTracking,
  reset
} from '../../features/orders/orderSlice';
import Spinner from '../../components/Spinner';
import Modal from '../../components/Modal';

const OrderList = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { orders, isLoading, isError, isSuccess, message, page, pages, total } = useSelector(
    (state) => state.orders
  );
  const { user } = useSelector((state) => state.auth);

  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState('');
  const [approvalFilter, setApprovalFilter] = useState('');
  const [paymentStatusFilter, setPaymentStatusFilter] = useState('');

  // Modal states
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [showRejectPaymentModal, setShowRejectPaymentModal] = useState(false);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [showTrackingModal, setShowTrackingModal] = useState(false);

  // Form states
  const [rejectionReason, setRejectionReason] = useState('');
  const [paymentRejectionReason, setPaymentRejectionReason] = useState('');
  const [statusNote, setStatusNote] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [trackingInfo, setTrackingInfo] = useState({
    trackingNumber: '',
    carrier: '',
    estimatedDeliveryDate: '',
    trackingUrl: ''
  });

  // Selected order
  const [selectedOrderId, setSelectedOrderId] = useState(null);

  useEffect(() => {
    // Check for admin role
    const userRole = user?.role || (user?.data && user.data.role);
    const isUserAdmin = userRole === 'admin' || user?.isAdmin === true;

    if (!user || !isUserAdmin) {
      navigate('/login');
      return;
    }

    // Fetch orders with filters
    dispatch(
      getOrders({
        page: currentPage,
        limit: 10,
        status: statusFilter,
        approvalStatus: approvalFilter,
        paymentStatus: paymentStatusFilter,
      })
    );

    // Reset state on component unmount
    return () => {
      dispatch(reset());
    };
  }, [dispatch, navigate, user, currentPage, statusFilter, approvalFilter, paymentStatusFilter]);

  // Handle success and error states
  useEffect(() => {
    if (isError) {
      toast.error(message);
    }

    if (isSuccess && message) {
      toast.success(message);
    }
  }, [isError, isSuccess, message]);

  const handleSearch = (e) => {
    e.preventDefault();
    // Implement search functionality
    // This would typically filter orders by customer name, order ID, etc.
    toast.info('Search functionality would be implemented here');
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleViewOrder = (id) => {
    navigate(`/order/${id}`);
  };

  const handleApproveOrder = (id) => {
    if (window.confirm('Are you sure you want to approve this order?')) {
      dispatch(approveOrder(id))
        .unwrap()
        .then(() => {
          toast.success('Order approved successfully');
          // Refresh the orders list
          dispatch(
            getOrders({
              page: currentPage,
              limit: 10,
              status: statusFilter,
              approvalStatus: approvalFilter,
            })
          );
        })
        .catch((error) => {
          toast.error(`Error approving order: ${error}`);
        });
    }
  };

  const openRejectModal = (id) => {
    setSelectedOrderId(id);
    setRejectionReason('');
    setShowRejectModal(true);
  };

  const handleRejectOrder = () => {
    if (rejectionReason.trim() === '') {
      toast.error('Please provide a reason for rejection');
      return;
    }

    dispatch(rejectOrder({ id: selectedOrderId, reason: rejectionReason }))
      .unwrap()
      .then(() => {
        toast.success('Order rejected successfully');
        setShowRejectModal(false);
        // Refresh the orders list
        dispatch(
          getOrders({
            page: currentPage,
            limit: 10,
            status: statusFilter,
            approvalStatus: approvalFilter,
            paymentStatus: paymentStatusFilter,
          })
        );
      })
      .catch((error) => {
        toast.error(`Error rejecting order: ${error}`);
      });
  };

  // Payment approval/rejection handlers
  const handleApprovePayment = (id) => {
    if (window.confirm('Are you sure you want to approve this payment?')) {
      dispatch(approvePayment({ id, note: 'Payment approved by admin' }))
        .unwrap()
        .then(() => {
          toast.success('Payment approved successfully');
          // Refresh the orders list
          dispatch(
            getOrders({
              page: currentPage,
              limit: 10,
              status: statusFilter,
              approvalStatus: approvalFilter,
              paymentStatus: paymentStatusFilter,
            })
          );
        })
        .catch((error) => {
          toast.error(`Error approving payment: ${error}`);
        });
    }
  };

  const openRejectPaymentModal = (id) => {
    setSelectedOrderId(id);
    setPaymentRejectionReason('');
    setShowRejectPaymentModal(true);
  };

  const handleRejectPayment = () => {
    if (paymentRejectionReason.trim() === '') {
      toast.error('Please provide a reason for payment rejection');
      return;
    }

    dispatch(rejectPayment({ id: selectedOrderId, reason: paymentRejectionReason }))
      .unwrap()
      .then(() => {
        toast.success('Payment rejected successfully');
        setShowRejectPaymentModal(false);
        // Refresh the orders list
        dispatch(
          getOrders({
            page: currentPage,
            limit: 10,
            status: statusFilter,
            approvalStatus: approvalFilter,
            paymentStatus: paymentStatusFilter,
          })
        );
      })
      .catch((error) => {
        toast.error(`Error rejecting payment: ${error}`);
      });
  };

  // Order status update handlers
  const openStatusModal = (id) => {
    setSelectedOrderId(id);
    setSelectedStatus('');
    setStatusNote('');
    setShowStatusModal(true);
  };

  const handleUpdateStatus = () => {
    if (!selectedStatus) {
      toast.error('Please select a status');
      return;
    }

    dispatch(updateOrderStatus({
      id: selectedOrderId,
      status: selectedStatus,
      note: statusNote
    }))
      .unwrap()
      .then(() => {
        toast.success('Order status updated successfully');
        setShowStatusModal(false);
        // Refresh the orders list
        dispatch(
          getOrders({
            page: currentPage,
            limit: 10,
            status: statusFilter,
            approvalStatus: approvalFilter,
            paymentStatus: paymentStatusFilter,
          })
        );
      })
      .catch((error) => {
        toast.error(`Error updating order status: ${error}`);
      });
  };

  // Delivery tracking handlers
  const openTrackingModal = (id) => {
    setSelectedOrderId(id);
    setTrackingInfo({
      trackingNumber: '',
      carrier: '',
      estimatedDeliveryDate: '',
      trackingUrl: ''
    });
    setShowTrackingModal(true);
  };

  const handleUpdateTracking = () => {
    if (!trackingInfo.trackingNumber || !trackingInfo.carrier) {
      toast.error('Please provide tracking number and carrier');
      return;
    }

    dispatch(updateDeliveryTracking({
      id: selectedOrderId,
      ...trackingInfo
    }))
      .unwrap()
      .then(() => {
        toast.success('Delivery tracking updated successfully');
        setShowTrackingModal(false);
        // Refresh the orders list
        dispatch(
          getOrders({
            page: currentPage,
            limit: 10,
            status: statusFilter,
            approvalStatus: approvalFilter,
            paymentStatus: paymentStatusFilter,
          })
        );
      })
      .catch((error) => {
        toast.error(`Error updating delivery tracking: ${error}`);
      });
  };

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get status badge color
  const getStatusBadgeColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get approval status badge color
  const getApprovalBadgeColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get payment status badge color
  const getPaymentStatusBadgeColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-blue-100 text-blue-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold">Orders Management</h1>
        </div>

        <div className="bg-gray-50 p-4 rounded-lg shadow-sm mb-4">
          <h2 className="text-lg font-semibold mb-3 flex items-center">
            <FaFilter className="mr-2" /> Filter Orders
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Order Status</label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Order Statuses</option>
                <option value="pending">Pending</option>
                <option value="processing">Processing</option>
                <option value="shipped">Shipped</option>
                <option value="delivered">Delivered</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Approval Status</label>
              <select
                value={approvalFilter}
                onChange={(e) => setApprovalFilter(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Approval Statuses</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Payment Status</label>
              <select
                value={paymentStatusFilter}
                onChange={(e) => setPaymentStatusFilter(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Payment Statuses</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-4 border-b">
          <form onSubmit={handleSearch} className="flex">
            <input
              type="text"
              placeholder="Search orders..."
              className="flex-grow border border-gray-300 rounded-l-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-r-md"
            >
              <FaSearch />
            </button>
          </form>
        </div>

        {isLoading ? (
          <div className="p-8 text-center">
            <Spinner />
          </div>
        ) : isError ? (
          <div className="p-8 text-center">
            <p className="text-red-500">{message}</p>
          </div>
        ) : orders.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-500">No orders found.</p>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Order ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Customer
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Order Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Payment Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Approval Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {orders.map((order) => (
                    <tr key={order._id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {order._id.substring(0, 8)}...
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(order.createdAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {order.user?.name || 'Unknown'}
                        </div>
                        <div className="text-sm text-gray-500">{order.user?.email || 'No email'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ₹{order.totalPrice?.toFixed(2) || '0.00'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeColor(
                            order.status
                          )}`}
                        >
                          {order.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getPaymentStatusBadgeColor(
                            order.paymentStatus || (order.isPaid ? 'approved' : 'pending')
                          )}`}
                        >
                          {order.paymentStatus || (order.isPaid ? 'approved' : 'pending')}
                        </span>
                        {order.isPaid && (
                          <div className="text-xs text-gray-500 mt-1">
                            {formatDate(order.paidAt)}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getApprovalBadgeColor(
                            order.approvalStatus
                          )}`}
                        >
                          {order.approvalStatus}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex flex-wrap gap-2">
                          {/* View Order */}
                          <button
                            onClick={() => handleViewOrder(order._id)}
                            className="text-indigo-600 hover:text-indigo-900 p-1 rounded-full hover:bg-indigo-50"
                            title="View Order Details"
                          >
                            <FaEye />
                          </button>

                          {/* Order Approval Actions */}
                          {order.approvalStatus === 'pending' && (
                            <>
                              <button
                                onClick={() => handleApproveOrder(order._id)}
                                className="text-green-600 hover:text-green-900 p-1 rounded-full hover:bg-green-50"
                                title="Approve Order"
                              >
                                <FaCheck />
                              </button>
                              <button
                                onClick={() => openRejectModal(order._id)}
                                className="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-red-50"
                                title="Reject Order"
                              >
                                <FaTimes />
                              </button>
                            </>
                          )}

                          {/* Payment Actions */}
                          {(order.paymentStatus === 'pending' || (!order.paymentStatus && !order.isPaid)) && (
                            <>
                              <button
                                onClick={() => handleApprovePayment(order._id)}
                                className="text-blue-600 hover:text-blue-900 p-1 rounded-full hover:bg-blue-50"
                                title="Approve Payment"
                              >
                                <FaMoneyBillWave />
                              </button>
                              <button
                                onClick={() => openRejectPaymentModal(order._id)}
                                className="text-orange-600 hover:text-orange-900 p-1 rounded-full hover:bg-orange-50"
                                title="Reject Payment"
                              >
                                <FaCreditCard />
                              </button>
                            </>
                          )}

                          {/* Order Status Actions */}
                          <button
                            onClick={() => openStatusModal(order._id)}
                            className="text-purple-600 hover:text-purple-900 p-1 rounded-full hover:bg-purple-50"
                            title="Update Order Status"
                          >
                            <FaFilter />
                          </button>

                          {/* Delivery Tracking Actions */}
                          {['processing', 'shipped'].includes(order.status) && (
                            <button
                              onClick={() => openTrackingModal(order._id)}
                              className="text-teal-600 hover:text-teal-900 p-1 rounded-full hover:bg-teal-50"
                              title="Update Delivery Tracking"
                            >
                              <FaShippingFast />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {pages > 1 && (
              <div className="px-6 py-4 flex justify-center">
                <nav className="flex space-x-2">
                  {[...Array(pages).keys()].map((x) => (
                    <button
                      key={x + 1}
                      onClick={() => handlePageChange(x + 1)}
                      className={`px-3 py-1 rounded-md ${
                        x + 1 === currentPage
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                      }`}
                    >
                      {x + 1}
                    </button>
                  ))}
                </nav>
              </div>
            )}
          </>
        )}
      </div>

      {/* Reject Order Modal */}
      {showRejectModal && (
        <Modal
          title="Reject Order"
          onClose={() => setShowRejectModal(false)}
          onConfirm={handleRejectOrder}
          confirmText="Reject Order"
          confirmColor="red"
        >
          <div className="mb-4">
            <label className="block text-gray-700 font-medium mb-2">
              Reason for Rejection
            </label>
            <textarea
              className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows="4"
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              placeholder="Please provide a reason for rejecting this order"
            ></textarea>
          </div>
        </Modal>
      )}

      {/* Reject Payment Modal */}
      {showRejectPaymentModal && (
        <Modal
          title="Reject Payment"
          onClose={() => setShowRejectPaymentModal(false)}
          onConfirm={handleRejectPayment}
          confirmText="Reject Payment"
          confirmColor="red"
        >
          <div className="mb-4">
            <label className="block text-gray-700 font-medium mb-2">
              Reason for Payment Rejection
            </label>
            <textarea
              className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows="4"
              value={paymentRejectionReason}
              onChange={(e) => setPaymentRejectionReason(e.target.value)}
              placeholder="Please provide a reason for rejecting this payment"
            ></textarea>
          </div>
        </Modal>
      )}

      {/* Update Order Status Modal */}
      {showStatusModal && (
        <Modal
          title="Update Order Status"
          onClose={() => setShowStatusModal(false)}
          onConfirm={handleUpdateStatus}
          confirmText="Update Status"
          confirmColor="blue"
        >
          <div className="mb-4">
            <label className="block text-gray-700 font-medium mb-2">
              New Status
            </label>
            <select
              className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
            >
              <option value="">Select Status</option>
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="shipped">Shipped</option>
              <option value="delivered">Delivered</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          <div className="mb-4">
            <label className="block text-gray-700 font-medium mb-2">
              Status Note (Optional)
            </label>
            <textarea
              className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows="3"
              value={statusNote}
              onChange={(e) => setStatusNote(e.target.value)}
              placeholder="Add a note about this status change"
            ></textarea>
          </div>
        </Modal>
      )}

      {/* Update Delivery Tracking Modal */}
      {showTrackingModal && (
        <Modal
          title="Update Delivery Tracking"
          onClose={() => setShowTrackingModal(false)}
          onConfirm={handleUpdateTracking}
          confirmText="Update Tracking"
          confirmColor="green"
        >
          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="block text-gray-700 font-medium mb-2">
                Tracking Number *
              </label>
              <input
                type="text"
                className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={trackingInfo.trackingNumber}
                onChange={(e) => setTrackingInfo({...trackingInfo, trackingNumber: e.target.value})}
                placeholder="Enter tracking number"
                required
              />
            </div>
            <div>
              <label className="block text-gray-700 font-medium mb-2">
                Carrier *
              </label>
              <input
                type="text"
                className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={trackingInfo.carrier}
                onChange={(e) => setTrackingInfo({...trackingInfo, carrier: e.target.value})}
                placeholder="Enter carrier name (e.g., FedEx, UPS)"
                required
              />
            </div>
            <div>
              <label className="block text-gray-700 font-medium mb-2">
                Estimated Delivery Date
              </label>
              <input
                type="date"
                className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={trackingInfo.estimatedDeliveryDate}
                onChange={(e) => setTrackingInfo({...trackingInfo, estimatedDeliveryDate: e.target.value})}
              />
            </div>
            <div>
              <label className="block text-gray-700 font-medium mb-2">
                Tracking URL
              </label>
              <input
                type="url"
                className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={trackingInfo.trackingUrl}
                onChange={(e) => setTrackingInfo({...trackingInfo, trackingUrl: e.target.value})}
                placeholder="Enter tracking URL"
              />
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default OrderList;
