import { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { FaUsers, FaMoneyBillWave, FaShoppingBag, FaChartLine } from 'react-icons/fa';
import { toast } from 'react-toastify';
import Spinner from '../components/Spinner';
import BinaryTree from '../components/BinaryTree';
import { getUserProfile } from '../features/auth/authSlice';
import { getUserDownline } from '../features/mlm/mlmSlice';
import { getMyOrders } from '../features/orders/orderSlice';

const Dashboard = () => {
  const dispatch = useDispatch();

  // Get data from Redux store
  const { user } = useSelector((state) => state.auth);
  const { downline, isLoading: mlmLoading, isError: mlmError, message: mlmMessage } = useSelector(
    (state) => state.mlm
  );
  const { orders, isLoading: ordersLoading, isError: ordersError, message: ordersMessage } = useSelector(
    (state) => state.orders
  );

  // Local state for stats
  const [stats, setStats] = useState({
    leftPV: 0,
    rightPV: 0,
    personalPV: 0,
    walletBalance: 0,
    totalCommissions: 0,
    totalOrders: 0,
  });

  // Calculate loading state
  const isLoading = mlmLoading || ordersLoading;

  // Handle errors
  useEffect(() => {
    if (mlmError) {
      toast.error(mlmMessage);
    }
    if (ordersError) {
      toast.error(ordersMessage);
    }
  }, [mlmError, mlmMessage, ordersError, ordersMessage]);

  // Fetch dashboard data
  useEffect(() => {
    console.log('Dashboard - Fetching data');

    // Fetch user profile
    dispatch(getUserProfile())
      .unwrap()
      .then(result => {
        console.log('Dashboard - User profile fetched successfully:', result);
      })
      .catch(error => {
        console.error('Dashboard - Error fetching user profile:', error);
        toast.error(`Error fetching profile: ${error.message || 'Unknown error'}`);
      });

    // Fetch downline tree
    dispatch(getUserDownline())
      .unwrap()
      .then(result => {
        console.log('Dashboard - Downline fetched successfully:', result);
      })
      .catch(error => {
        console.error('Dashboard - Error fetching downline:', error);
        toast.error(`Error fetching downline: ${error.message || 'Unknown error'}`);
      });

    // Fetch orders
    dispatch(getMyOrders())
      .unwrap()
      .then(result => {
        console.log('Dashboard - Orders fetched successfully:', result);
      })
      .catch(error => {
        console.error('Dashboard - Error fetching orders:', error);
        toast.error(`Error fetching orders: ${error.message || 'Unknown error'}`);
      });
  }, [dispatch]);

  // Update stats when user data changes
  useEffect(() => {
    console.log('Dashboard - Updating stats with user:', user);
    console.log('Dashboard - Updating stats with orders:', orders);
    console.log('Dashboard - Updating stats with downline:', downline);

    if (user) {
      // First try to get PV values from the user object
      let leftPV = user.leftPV;
      let rightPV = user.rightPV;

      // If not available in user object, try to get from downline data
      if ((leftPV === undefined || rightPV === undefined) && downline) {
        leftPV = downline.leftPV;
        rightPV = downline.rightPV;
        console.log('Dashboard - Using PV values from downline:', { leftPV, rightPV });
      }

      setStats({
        leftPV: leftPV || 0,
        rightPV: rightPV || 0,
        personalPV: user.personalPV || 0,
        walletBalance: user.wallet?.balance || 0,
        totalCommissions: user.wallet?.transactions
          ?.filter(t => t.type === 'commission')
          ?.reduce((sum, t) => sum + t.amount, 0) || 0,
        totalOrders: orders?.length || 0,
      });
    }
  }, [user, orders, downline]);

  // Add a retry function for error cases
  const handleRetry = () => {
    // Clear any error states
    dispatch({ type: 'mlm/reset' });
    dispatch({ type: 'orders/reset' });

    // Refetch data
    dispatch(getUserProfile());
    dispatch(getUserDownline());
    dispatch(getMyOrders());
  };

  if (isLoading) {
    return <Spinner />;
  }

  if (mlmError || ordersError) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p className="font-bold">Error loading dashboard data</p>
          <p>{mlmMessage || ordersMessage}</p>
        </div>
        <button
          onClick={handleRetry}
          className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Welcome, {user.name}!</h1>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-full mr-4">
              <FaUsers className="text-blue-600 text-xl" />
            </div>
            <div>
              <p className="text-gray-500 text-sm">Network PV</p>
              <div className="flex items-center mt-1">
                <span className="text-xl font-bold">L: {stats.leftPV}</span>
                <span className="mx-2 text-gray-400">|</span>
                <span className="text-xl font-bold">R: {stats.rightPV}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-full mr-4">
              <FaMoneyBillWave className="text-green-600 text-xl" />
            </div>
            <div>
              <p className="text-gray-500 text-sm">Wallet Balance</p>
              <p className="text-xl font-bold">₹{stats.walletBalance.toFixed(2)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="bg-purple-100 p-3 rounded-full mr-4">
              <FaChartLine className="text-purple-600 text-xl" />
            </div>
            <div>
              <p className="text-gray-500 text-sm">Total Commissions</p>
              <p className="text-xl font-bold">₹{stats.totalCommissions.toFixed(2)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="bg-yellow-100 p-3 rounded-full mr-4">
              <FaShoppingBag className="text-yellow-600 text-xl" />
            </div>
            <div>
              <p className="text-gray-500 text-sm">Personal PV</p>
              <p className="text-xl font-bold">{stats.personalPV}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Binary Tree Visualization */}
      <div className="bg-white rounded-lg shadow p-6 mb-8">
        <h2 className="text-xl font-bold mb-4">Your Network</h2>
        <BinaryTree data={downline} />
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Commissions */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold mb-4">Recent Commissions</h2>
          <p className="text-gray-500">No recent commissions</p>
          {/* We'll implement this when the backend endpoint is available */}
        </div>

        {/* Recent Orders */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold mb-4">Recent Orders</h2>
          {orders && orders.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Order ID
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {orders.slice(0, 5).map((order) => (
                    <tr key={order._id}>
                      <td className="px-4 py-2 whitespace-nowrap">
                        {order._id.substring(0, 8)}...
                      </td>
                      <td className="px-4 py-2 whitespace-nowrap">
                        {new Date(order.createdAt).toLocaleDateString()}
                      </td>
                      <td className="px-4 py-2 whitespace-nowrap">
                        ₹{order.totalPrice.toFixed(2)}
                      </td>
                      <td className="px-4 py-2 whitespace-nowrap">
                        <span
                          className={`px-2 py-1 text-xs rounded-full ${
                            order.status === 'delivered'
                              ? 'bg-green-100 text-green-800'
                              : order.status === 'processing'
                              ? 'bg-blue-100 text-blue-800'
                              : order.status === 'cancelled'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}
                        >
                          {order.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="text-gray-500">No recent orders</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
